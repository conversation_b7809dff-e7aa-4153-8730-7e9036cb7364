import NextAuth from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import { MongoDBAdapter } from '@auth/mongodb-adapter';
import clientPromise from '../../../lib/mongodb.js';
import connectDB from '../../../lib/mongoose.js';
import UsuarioAcademy from '../../../models/userAcademyModel.js';

export const authOptions = {
  secret: process.env.SECRET,
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
  ],
  pages: {
    error: '/login',
  },
  callbacks: {
    async signIn({ user, account, profile }) {
      try {
        await connectDB();
        const existingUser = await UsuarioAcademy.findOne({ email: user.email });

        if (!existingUser) {
          await UsuarioAcademy.create({
            name: user.name,
            email: user.email,
            created: new Date(),
            canEnter: false,
            isActive: false,
            role: 'PENDIENTE'
          });
        }
      } catch (error) {
        console.error('Error al guardar/actualizar el usuario:', error);
        return false;
      }
      return true;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id;
        session.user.email = token.email;
      }
      return session;
    },
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.email = user.email;
      }
      return token;
    },
  },
};

export default NextAuth(authOptions);
