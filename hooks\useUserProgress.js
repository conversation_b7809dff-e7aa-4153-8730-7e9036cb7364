// hooks/useUserProgress.js
import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';

export const useUserProgress = () => {
  const { data: session } = useSession();
  const [progress, setProgress] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Cargar progreso inicial
  useEffect(() => {
    const loadProgress = async () => {
      if (!session?.user?.email) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        const response = await fetch(`/api/progress?userEmail=${session.user.email}`);
        
        if (!response.ok) {
          throw new Error('Error al cargar progreso');
        }
        
        const progressData = await response.json();
        setProgress(progressData);
        setError(null);
      } catch (err) {
        console.error('Error loading progress:', err);
        setError(err.message);
        
        // Fallback a datos por defecto
        setProgress({
          tiempoEstudio: 0,
          tiempoPagado: 0,
          videosCompletados: 0,
          totalVideos: 0,
          progresoCurso: 0,
          fechaInicio: new Date().toLocaleDateString('es-ES'),
          proximoCertificado: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toLocaleDateString('es-ES')
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadProgress();
  }, [session]);

  // Función para actualizar progreso
  const updateProgress = async (action, data) => {
    if (!session?.user?.email) {
      throw new Error('Usuario no autenticado');
    }

    try {
      const response = await fetch('/api/progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          data
        })
      });

      if (!response.ok) {
        throw new Error('Error al actualizar progreso');
      }

      // Recargar progreso después de actualizar
      const updatedResponse = await fetch(`/api/progress?userEmail=${session.user.email}`);
      const updatedProgress = await updatedResponse.json();
      setProgress(updatedProgress);

      return { success: true };
    } catch (err) {
      console.error('Error updating progress:', err);
      setError(err.message);
      throw err;
    }
  };

  // Funciones específicas para diferentes acciones
  const markVideoAsWatched = async (videoId, videoName, category, watchTime = 0, progressPercent = 100) => {
    return updateProgress('mark_video_watched', {
      videoId,
      videoName,
      category,
      watchTime,
      progressPercent
    });
  };

  const saveNote = async (videoId, note) => {
    return updateProgress('save_note', {
      videoId,
      note
    });
  };

  const toggleFavorite = async (videoId) => {
    return updateProgress('toggle_favorite', {
      videoId
    });
  };

  const addStudySession = async (duration, activity = 'video') => {
    return updateProgress('add_study_session', {
      duration,
      activity
    });
  };

  const updateProfile = async (profileData) => {
    return updateProgress('update_profile', profileData);
  };

  const recordLogin = async () => {
    return updateProgress('record_login', {});
  };

  const updateVideoTotals = async (categoryTotals) => {
    return updateProgress('update_video_totals', {
      categoryTotals
    });
  };

  // Función para sincronizar datos de localStorage con la base de datos
  const syncLocalStorageToDatabase = async () => {
    if (!session?.user?.email) return;

    try {
      // Obtener datos de localStorage
      const savedWatched = localStorage.getItem('watchedVideos');
      const savedFavorites = localStorage.getItem('favoriteVideos');
      const savedNotes = localStorage.getItem('videoNotes');
      const savedProgress = localStorage.getItem('videoProgress');

      if (savedWatched) {
        const watchedVideos = JSON.parse(savedWatched);
        for (const videoId of watchedVideos) {
          await markVideoAsWatched(videoId, videoId, 'unknown', 0, 100);
        }
      }

      if (savedFavorites) {
        const favoriteVideos = JSON.parse(savedFavorites);
        for (const videoId of favoriteVideos) {
          await toggleFavorite(videoId);
        }
      }

      if (savedNotes) {
        const notes = JSON.parse(savedNotes);
        for (const [videoId, note] of Object.entries(notes)) {
          if (note.trim()) {
            await saveNote(videoId, note);
          }
        }
      }

      console.log('Sincronización completada');
    } catch (error) {
      console.error('Error en sincronización:', error);
    }
  };

  // Función para limpiar localStorage después de sincronizar
  const clearLocalStorage = () => {
    localStorage.removeItem('watchedVideos');
    localStorage.removeItem('favoriteVideos');
    localStorage.removeItem('videoNotes');
    localStorage.removeItem('videoProgress');
  };

  return {
    progress,
    isLoading,
    error,
    updateProgress,
    markVideoAsWatched,
    saveNote,
    toggleFavorite,
    addStudySession,
    updateProfile,
    recordLogin,
    updateVideoTotals,
    syncLocalStorageToDatabase,
    clearLocalStorage,
    refetch: () => {
      if (session?.user?.email) {
        setIsLoading(true);
        loadProgress();
      }
    }
  };
};
