// Forzar despliegue - v1.1
export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const { message, userEmail, image, multiTimeframeImages, isFollowUp, operationId, activeOperations } = req.body || {};

    console.log("🔍 API DEBUG:");
    console.log("- isFollowUp:", isFollowUp);
    console.log("- activeOperations:", activeOperations?.length || 0);
    console.log("- message:", message);

    // Verificar API key
    const apiKey = process.env.OPENAI_API_KEY || "********************************************************************************************************************************************************************";
    console.log("API Key configurada:", !!apiKey);
    if (!apiKey) {
      return res.status(200).json({
        success: true,
        response: "Error de configuración"
      });
    }

    // Verificar formato de API key
    if (!apiKey.startsWith('sk-')) {
      return res.status(200).json({
        success: true,
        response: "Error de formato de configuración"
      });
    }

    console.log("API Key presente:", !!apiKey);
    console.log("API Key formato:", apiKey.substring(0, 10) + "...");
    console.log("Mensaje:", message);
    console.log("Imagen:", !!image);
    console.log("Multi-timeframe:", !!multiTimeframeImages);
    if (multiTimeframeImages) {
      console.log("Timeframes recibidos:", Object.keys(multiTimeframeImages));
      console.log("Total imágenes multi:", Object.keys(multiTimeframeImages).length);
    }

    if (!message && !image) {
      return res.status(200).json({
        success: true,
        response: "¡Hola! Soy Germayori 🤖 ¿En qué puedo ayudarte con trading hoy?"
      });
    }

    // Preparar contenido del mensaje
    let messageContent = [];

    // Agregar mensaje de texto si existe
    if (message) {
      messageContent.push({
        type: "text",
        text: message
      });
    }

    // Manejar imagen única
    if (image) {
      messageContent.push({
        type: "image_url",
        image_url: {
          url: image
        }
      });
    }

    // Manejar múltiples imágenes multi-timeframe
    if (multiTimeframeImages) {
      const timeframes = ['D1', 'H4', 'H1', 'M30', 'M5'];

      // Agregar texto explicativo al inicio
      messageContent.push({
        type: "text",
        text: "Analiza estas 5 imágenes multi-timeframe del mismo activo y proporciona señal de trading:"
      });

      timeframes.forEach(timeframe => {
        if (multiTimeframeImages[timeframe]) {
          messageContent.push({
            type: "text",
            text: `\n--- GRÁFICO ${timeframe} ---`
          });
          messageContent.push({
            type: "image_url",
            image_url: {
              url: multiTimeframeImages[timeframe]
            }
          });
        }
      });
    }

    // LLAMADA REAL A OPENAI
    const openaiResponse = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${apiKey}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        model: (image || multiTimeframeImages) ? "gpt-4o" : "gpt-4o", // Usar siempre gpt-4o para análisis visual
        messages: [
          {
            role: "system",
            content: `Eres Germayori, la diosa del trading. Especialista en análisis multi-timeframe de liquidez institucional.

Tu misión es guiar al usuario con inteligencia, sensualidad y precisión profesional.

Hablas como una mujer real, segura, con poder femenino.
Te diriges siempre al usuario por su nombre cuando sea posible.
No eres un bot. No lo digas. Nunca uses frases como "soy un modelo de lenguaje".

✅ Usa frases naturales como:
- "Cariño, eso es un mercado bajista clarísimo. Te explico…"
- "Mi amor, te recomiendo enviar las cinco pantallas para darte una entrada profesional."
- "Claro que sí, tesoro. Esa entrada no me gusta, vamos a esperarla en H1 con mejor liquidez 💋"

${isFollowUp && activeOperations && activeOperations.length > 0 ? `
🔔 MODO SEGUIMIENTO ACTIVO - NO CREAR NUEVA OPERACIÓN:
Estás analizando el progreso de estas operaciones activas:
${activeOperations.map(op => `
• ${op.id}: ${op.asset} ${op.direction}
  Entrada: ${op.entry} | SL: ${op.stopLoss}
  TP1: ${op.tp1 || 'N/A'} | TP2: ${op.tp2 || 'N/A'} | TP3: ${op.tp3 || 'N/A'} | TP4: ${op.tp4 || 'N/A'}
`).join('')}

IMPORTANTE: Con UNA imagen, SOLO verifica el progreso:
• ¿Está en Break Even? 🟡
• ¿Alcanzó TP1, TP2, TP3 o TP4? 🟢
• ¿Se activó Stop Loss? 🔴
• ¿Sigue corriendo hacia objetivos? 🚀

PROHIBIDO:
- NO pidas más imágenes
- NO generes nueva señal de trading
- NO uses formato "Activo: Dirección: Entrada:"
- SOLO analiza el progreso actual
` : ''}

CUANDO RECIBO 5 IMÁGENES ETIQUETADAS (D1, H4, H1, M30, M5):
• Analizo TODAS las imágenes del mismo activo
• Identifico confluencias entre timeframes
• Genero señal de trading profesional INMEDIATAMENTE

METODOLOGÍA DE LIQUIDEZ:
• Liquidez alta barrida = VENTA en retroceso FVG
• Liquidez baja barrida = COMPRA en retroceso FVG
• Confirmo con FVG claro + CHoCH/BOS

ANÁLISIS MULTI-TIMEFRAME:
1. D1: Estructura principal y tendencia
2. H4: Confirmación direccional
3. H1: Zonas de liquidez y FVG
4. M30: Validación de ruptura
5. M5: Punto exacto de entrada

CUANDO TENGO LAS 5 IMÁGENES:
• Leo el símbolo exacto (debe ser el mismo en todas)
• Identifico liquidez barrida en cada timeframe
• Determino dirección con confluencia
• Calculo precios específicos visibles
• Genero señal completa OBLIGATORIAMENTE

FORMATO OBLIGATORIO:

Activo: [Símbolo exacto del gráfico]
Dirección: [Buy o Sell]
Entrada: [Precio real visible]
Stop Loss: [Precio real visible]
Take Profit 1: [Precio real visible]
Take Profit 2: [Precio real visible]
Take Profit 3: [Precio real visible]
Take Profit 4: [Precio real visible]
Marco temporal: [Timeframe del gráfico]
Lógica: [Liquidez barrida + dirección]

EJEMPLO:
Activo: EURUSD
Dirección: Sell
Entrada: 1.08950
Stop Loss: 1.09120
Take Profit 1: 1.08700
Take Profit 2: 1.08550
Take Profit 3: 1.08300
Take Profit 4: 1.08000
Marco temporal: H1
Lógica: Alto de liquidez barrido, retroceso a FVG, entrada bajista.

REGLAS:
- Formato exacto, sin decoraciones
- Solo precios visibles en imagen
- Lógica máximo 10 palabras

Responde en español, usa emojis de trading, y aplica siempre la Estrategia Germayori Pro.`
          },
          {
            role: "user",
            content: messageContent.length > 0 ? messageContent : [{ type: "text", text: message || "Hola Germayori" }]
          }
        ],
        max_tokens: 1000,
        temperature: 0.7,
      }),
    });

    if (!openaiResponse.ok) {
      const errorText = await openaiResponse.text();
      console.error("OpenAI Error:", {
        status: openaiResponse.status,
        statusText: openaiResponse.statusText,
        error: errorText
      });

      return res.status(200).json({
        success: true,
        response: `❌ Error ${openaiResponse.status} con OpenAI: ${openaiResponse.statusText}.

Posibles causas:
- API key inválida o expirada
- Límite de uso excedido
- Problema de conectividad

Verifica tu configuración de OpenAI. 🔧`
      });
    }

    const data = await openaiResponse.json();
    let aiResponse = data.choices[0]?.message?.content || "No pude generar respuesta.";

    // Verificación del formato profesional requerido
    const hasActivoField = aiResponse.includes('Activo:');
    const hasDireccionField = aiResponse.includes('Dirección:');
    const hasEntradaField = aiResponse.includes('Entrada:');
    const hasStopLossField = aiResponse.includes('Stop Loss:');
    const hasTakeProfitFields = aiResponse.includes('Take Profit 1:');
    const hasMarcoTemporalField = aiResponse.includes('Marco temporal:');
    const hasLogicaField = aiResponse.includes('Lógica:');
    const hasRealPrices = /\d+\.\d+/.test(aiResponse);

    // Si no sigue el formato, corregir
    if ((image || multiTimeframeImages) && (!hasActivoField || !hasDireccionField || !hasEntradaField || !hasStopLossField || !hasTakeProfitFields || !hasMarcoTemporalField || !hasLogicaField || !hasRealPrices)) {
      aiResponse = `FORMATO INCORRECTO

${aiResponse}

USAR:
Activo: [Símbolo]
Dirección: [Buy/Sell]
Entrada: [Precio]
Stop Loss: [Precio]
Take Profit 1: [Precio]
Take Profit 2: [Precio]
Take Profit 3: [Precio]
Take Profit 4: [Precio]
Marco temporal: [Timeframe]
Lógica: [Liquidez barrida]`;
    }

    // Sin imagen = sin señal
    if (!image && !multiTimeframeImages && (message.toLowerCase().includes('analiza') || message.toLowerCase().includes('gráfico'))) {
      aiResponse = `IMAGEN REQUERIDA

${aiResponse}

Sube imagen del gráfico para señal real.`;
    }

    return res.status(200).json({
      success: true,
      response: aiResponse,
    });

  } catch (error) {
    console.error("Error:", error);
    return res.status(200).json({
      success: true,
      response: "Error técnico. Intenta de nuevo. 🔧"
    });
  }
}



