import React, { useState, useRef, useEffect } from "react";
import PrivateRoute from "../components/privateRoute";
import { useSession } from "next-auth/react";

const GermayoriPage = () => {
  const { data: session } = useSession();
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [recording, setRecording] = useState(false);
  const [isVoiceMessage, setIsVoiceMessage] = useState(false);
  const recognitionRef = useRef(null);

  // Estados para seguimiento de múltiples trades
  const [activeTradesFollowUp, setActiveTradesFollowUp] = useState([]);
  const [followUpTimers, setFollowUpTimers] = useState({});

  // Estados para las 5 imágenes multi-timeframe
  const [timeframeImages, setTimeframeImages] = useState({
    D1: { file: null, preview: null },
    H4: { file: null, preview: null },
    H1: { file: null, preview: null },
    M30: { file: null, preview: null },
    M5: { file: null, preview: null }
  });

  // Estados para múltiples operaciones
  const [multipleOperations, setMultipleOperations] = useState([]);
  const [operationCounter, setOperationCounter] = useState(1);
  const [showOperationsPanel, setShowOperationsPanel] = useState(false);

  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Función para iniciar seguimiento de múltiples trades
  const startTradeFollowUp = (tradeInfo) => {
    const operationId = `OP-${operationCounter}`;

    // Crear nueva operación
    const newOperation = {
      id: operationId,
      ...tradeInfo,
      startTime: new Date(),
      followUpCount: 0,
      status: 'active' // active, completed, stopped
    };

    // Agregar a operaciones activas
    setActiveTradesFollowUp(prev => [...prev, newOperation]);
    setOperationCounter(prev => prev + 1);

    // Timer para pedir capturas cada 30 minutos
    const timer = setInterval(() => {
      requestFollowUpScreenshot(operationId);
    }, 30 * 60 * 1000); // 30 minutos en milisegundos

    // Guardar timer con ID único
    setFollowUpTimers(prev => ({
      ...prev,
      [operationId]: timer
    }));
  };

  // Función para solicitar captura de seguimiento específico
  const requestFollowUpScreenshot = (operationId) => {
    const operation = activeTradesFollowUp.find(op => op.id === operationId);
    if (!operation || operation.status !== 'active') return;

    const followUpMessage = {
      role: "assistant",
      content: `🔔 **SEGUIMIENTO ${operation.id} - ${operation.asset}**

⏰ **VERIFICACIÓN #${operation.followUpCount + 1}** (${(operation.followUpCount + 1) * 30} minutos)

📊 **OPERACIÓN ACTIVA:**
• **ID**: ${operation.id}
• **Activo**: ${operation.asset} ${operation.direction}
• **Entrada**: ${operation.entry}
• **Stop Loss**: ${operation.stopLoss}
• **TP1**: ${operation.tp1 || 'N/A'} | **TP2**: ${operation.tp2 || 'N/A'}
• **TP3**: ${operation.tp3 || 'N/A'} | **TP4**: ${operation.tp4 || 'N/A'}

📸 **NECESITO CAPTURA ACTUAL**

Sube una imagen del gráfico **${operation.asset}** para que pueda analizar:

🔍 **VERIFICARÉ:**
• ¿Está en **Break Even**? 🟡
• ¿Alcanzó **TP1, TP2, TP3 o TP4**? 🟢
• ¿Se activó el **Stop Loss**? 🔴
• ¿Sigue corriendo hacia objetivos? 🚀

💋 **MI DECISIÓN SERÁ:**
• **CERRAR**: Si alcanzó TP4 o Stop Loss
• **DEJAR CORRER**: Si va hacia los objetivos
• **TOMAR PARCIALES**: Si está en TP1, TP2 o TP3
• **MOVER SL**: Para proteger ganancias

📸 **SUBE LA CAPTURA AHORA** para continuar el seguimiento.

*Menciona "${operation.id}" en tu mensaje para identificar la operación.*`
    };

    setMessages(prev => [...prev, followUpMessage]);

    // Incrementar contador de seguimientos
    setActiveTradesFollowUp(prev =>
      prev.map(op =>
        op.id === operationId
          ? { ...op, followUpCount: op.followUpCount + 1 }
          : op
      )
    );
  };

  // Función para cerrar operación
  const closeOperation = (operationId, reason = 'manual') => {
    // Detener timer
    if (followUpTimers[operationId]) {
      clearInterval(followUpTimers[operationId]);
      setFollowUpTimers(prev => {
        const newTimers = { ...prev };
        delete newTimers[operationId];
        return newTimers;
      });
    }

    // Marcar operación como completada
    setActiveTradesFollowUp(prev =>
      prev.map(op =>
        op.id === operationId
          ? { ...op, status: 'completed', closeReason: reason, closeTime: new Date() }
          : op
      )
    );
  };

  // Limpiar timers al desmontar componente
  useEffect(() => {
    return () => {
      Object.values(followUpTimers).forEach(timer => {
        if (timer) clearInterval(timer);
      });
    };
  }, [followUpTimers]);

  // Mensaje inicial de Germayori
  useEffect(() => {
    const initialMessage = {
      role: "assistant",
      content: `¡Hola! Soy Germayori, especialista en análisis de liquidez institucional.

🎯 **ANÁLISIS MULTI-TIMEFRAME PROFESIONAL**

Usa el panel de arriba para subir 5 imágenes del MISMO activo:

📊 **TIMEFRAMES REQUERIDOS:**
• **D1** - Estructura principal y tendencia
• **H4** - Confirmación de dirección
• **H1** - Zonas de liquidez y FVG
• **M30** - Validación de ruptura
• **M5** - Punto exacto de entrada

⚠️ **INSTRUCCIONES:**
1. Sube las 5 imágenes del mismo par (ej: XAUUSD)
2. Haz clic en "Generar Señal de Trading"
3. Recibirás análisis profesional con formato:

Activo: [PAR]
Dirección: [Buy/Sell]
Entrada: [Precio]
Stop Loss: [Precio]
Take Profit 1-4: [Precios]
Marco temporal: [Confluencia]
Lógica: [Análisis de liquidez]

🚀 **¡Comienza subiendo las imágenes arriba!**`,
      timestamp: new Date().toLocaleTimeString()
    };

    setMessages([initialMessage]);
  }, []);

  // Función para comprimir imagen universal (funciona en todos los dispositivos)
  const compressImage = (file, maxSizeKB = 800, quality = 0.8) => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calcular nuevas dimensiones manteniendo aspect ratio
        let { width, height } = img;
        const maxDimension = 1200; // Máximo ancho/alto

        if (width > height) {
          if (width > maxDimension) {
            height = (height * maxDimension) / width;
            width = maxDimension;
          }
        } else {
          if (height > maxDimension) {
            width = (width * maxDimension) / height;
            height = maxDimension;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Dibujar imagen redimensionada
        ctx.drawImage(img, 0, 0, width, height);

        // Función recursiva para ajustar calidad hasta lograr tamaño deseado
        const tryCompress = (currentQuality) => {
          canvas.toBlob((blob) => {
            const sizeKB = blob.size / 1024;
            console.log(`🖼️ Compresión: ${sizeKB.toFixed(1)}KB con calidad ${currentQuality}`);

            if (sizeKB <= maxSizeKB || currentQuality <= 0.1) {
              // Convertir blob a base64
              const reader = new FileReader();
              reader.onload = () => resolve(reader.result);
              reader.readAsDataURL(blob);
            } else {
              // Reducir calidad y reintentar
              tryCompress(currentQuality - 0.1);
            }
          }, 'image/jpeg', currentQuality);
        };

        tryCompress(quality);
      };

      // Cargar imagen original
      const reader = new FileReader();
      reader.onload = (e) => {
        img.src = e.target.result;
      };
      reader.readAsDataURL(file);
    });
  };

  const handleImageUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedImage(file);

      // Comprimir imagen antes de mostrar preview
      try {
        const compressedImage = await compressImage(file);
        setImagePreview(compressedImage);
        console.log(`✅ Imagen comprimida exitosamente`);
      } catch (error) {
        console.error('Error comprimiendo imagen:', error);
        // Fallback al método original si falla la compresión
        const reader = new FileReader();
        reader.onload = (e) => {
          setImagePreview(e.target.result);
        };
        reader.readAsDataURL(file);
      }
    }
  };

  const removeImage = () => {
    setSelectedImage(null);
    setImagePreview(null);
  };

  // Función para subir imagen por timeframe específico con compresión
  const handleTimeframeImageUpload = async (timeframe, e) => {
    const file = e.target.files[0];
    if (file) {
      try {
        // Comprimir imagen antes de guardar
        const compressedImage = await compressImage(file);
        setTimeframeImages(prev => ({
          ...prev,
          [timeframe]: {
            file: file,
            preview: compressedImage
          }
        }));
        console.log(`✅ Imagen ${timeframe} comprimida exitosamente`);
      } catch (error) {
        console.error(`Error comprimiendo imagen ${timeframe}:`, error);
        // Fallback al método original si falla la compresión
        const reader = new FileReader();
        reader.onload = (e) => {
          setTimeframeImages(prev => ({
            ...prev,
            [timeframe]: {
              file: file,
              preview: e.target.result
            }
          }));
        };
        reader.readAsDataURL(file);
      }
    }
  };

  // Función para remover imagen de timeframe específico
  const removeTimeframeImage = (timeframe) => {
    setTimeframeImages(prev => ({
      ...prev,
      [timeframe]: { file: null, preview: null }
    }));
  };

  // Verificar si todas las imágenes están subidas
  const allImagesUploaded = Object.values(timeframeImages).every(img => img.file !== null);

  // Función para analizar las 5 imágenes multi-timeframe
  const handleAnalyzeMultiTimeframe = async () => {
    if (!allImagesUploaded) {
      alert("Por favor sube las 5 imágenes antes de analizar");
      return;
    }

    setIsLoading(true);

    // Crear mensaje con las 5 imágenes
    const userMessage = {
      role: "user",
      content: "📊 Análisis Multi-Timeframe",
      multiTimeframeImages: timeframeImages,
      hasMultiTimeframe: true
    };
    setMessages(prev => [...prev, userMessage]);

    try {
      // Preparar las imágenes para envío
      const imagesData = {};
      Object.keys(timeframeImages).forEach(timeframe => {
        if (timeframeImages[timeframe].preview) {
          imagesData[timeframe] = timeframeImages[timeframe].preview;
        }
      });

      console.log("Enviando imágenes:", Object.keys(imagesData));
      console.log("Total imágenes:", Object.keys(imagesData).length);

      const response = await fetch("/api/openai", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: `Análisis multi-timeframe: D1, H4, H1, M30, M5 del mismo activo. Proporciona señal de trading profesional.`,
          userEmail: session?.user?.email,
          multiTimeframeImages: imagesData,
        }),
      });

      const data = await response.json();

      if (data.success) {
        const aiMessage = {
          role: "assistant",
          content: data.response,
          isSignal: true
        };
        setMessages(prev => [...prev, aiMessage]);

        // Detectar si es una señal de trading para iniciar seguimiento
        const isTradeSignal = data.response.includes('Activo:') &&
                             data.response.includes('Dirección:') &&
                             data.response.includes('Entrada:') &&
                             data.response.includes('Stop Loss:');

        if (isTradeSignal) {
          // Extraer información completa del trade
          const lines = data.response.split('\n');
          const tradeInfo = {};

          lines.forEach(line => {
            if (line.includes('Activo:')) tradeInfo.asset = line.split(':')[1]?.trim();
            if (line.includes('Dirección:')) tradeInfo.direction = line.split(':')[1]?.trim();
            if (line.includes('Entrada:')) tradeInfo.entry = line.split(':')[1]?.trim();
            if (line.includes('Stop Loss:')) tradeInfo.stopLoss = line.split(':')[1]?.trim();
            if (line.includes('Take Profit 1:')) tradeInfo.tp1 = line.split(':')[1]?.trim();
            if (line.includes('Take Profit 2:')) tradeInfo.tp2 = line.split(':')[1]?.trim();
            if (line.includes('Take Profit 3:')) tradeInfo.tp3 = line.split(':')[1]?.trim();
            if (line.includes('Take Profit 4:')) tradeInfo.tp4 = line.split(':')[1]?.trim();
            if (line.includes('Marco temporal:')) tradeInfo.timeframe = line.split(':')[1]?.trim();
          });

          // Iniciar seguimiento automático
          if (tradeInfo.asset && tradeInfo.direction) {
            startTradeFollowUp(tradeInfo);

            // Mensaje de confirmación de seguimiento automático
            setTimeout(() => {
              const followUpConfirmation = {
                role: "assistant",
                content: `✅ **SEGUIMIENTO AUTOMÁTICO ACTIVADO**

🎯 **Operación**: ${tradeInfo.asset} ${tradeInfo.direction}
📊 **Entrada**: ${tradeInfo.entry} | **SL**: ${tradeInfo.stopLoss}
🎯 **Objetivos**: TP1: ${tradeInfo.tp1} | TP2: ${tradeInfo.tp2} | TP3: ${tradeInfo.tp3} | TP4: ${tradeInfo.tp4}

⏰ **SEGUIMIENTO CADA 30 MINUTOS**

Yo, Germayori, tomaré decisiones automáticas basadas en:
• **Tiempo transcurrido** desde la entrada
• **Probabilidades de mercado** institucional
• **Gestión de riesgo** profesional
• **Niveles de Take Profit** alcanzados

🔔 **NO NECESITAS HACER NADA**
Te diré exactamente qué hacer cada 30 minutos:
• Cuándo mover Stop Loss a Break Even
• Cuándo tomar ganancias parciales
• Cuándo dejar correr hacia TP4
• Cuándo cerrar la operación

¡Relájate, yo me encargo del seguimiento! 💋`
              };
              setMessages(prev => [...prev, followUpConfirmation]);
            }, 2000);
          }
        }

        // No hablar para análisis multi-timeframe (solo texto)
      } else {
        const errorMessage = {
          role: "assistant",
          content: "Error procesando análisis multi-timeframe. Intenta de nuevo."
        };
        setMessages(prev => [...prev, errorMessage]);
      }
    } catch (error) {
      const errorMessage = {
        role: "assistant",
        content: "Error de conexión en análisis multi-timeframe."
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() && !selectedImage) return;

    // Verificar si hay operaciones activas y es una imagen de seguimiento
    const hasActiveOperations = activeTradesFollowUp.filter(op => op.status === 'active').length > 0;
    const isFollowUpImage = selectedImage && hasActiveOperations; // Cualquier imagen cuando hay operaciones activas

    console.log("🔍 DEBUG SEGUIMIENTO:");
    console.log("- hasActiveOperations:", hasActiveOperations);
    console.log("- selectedImage:", !!selectedImage);
    console.log("- isFollowUpImage:", isFollowUpImage);
    console.log("- inputMessage:", inputMessage);

    const userMessage = {
      role: "user",
      content: inputMessage,
      image: imagePreview,
      hasImage: !!selectedImage,
      isFollowUp: isFollowUpImage
    };
    setMessages(prev => [...prev, userMessage]);

    const currentMessage = inputMessage;
    const wasVoiceMessage = isVoiceMessage;
    setInputMessage("");
    setSelectedImage(null);
    setImagePreview(null);
    setIsVoiceMessage(false); // Resetear la bandera de voz
    setIsLoading(true);

    try {
      const response = await fetch("/api/openai", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          message: currentMessage,
          userEmail: session?.user?.email,
          image: imagePreview,
          isFollowUp: isFollowUpImage,
          activeOperations: isFollowUpImage ? activeTradesFollowUp.filter(op => op.status === 'active') : null,
        }),
      });

      const data = await response.json();

      if (data.success) {
        const aiMessage = { role: "assistant", content: data.response };
        setMessages(prev => [...prev, aiMessage]);

        // Detectar si es una señal de trading para iniciar seguimiento
        // SOLO crear nueva operación si NO es imagen de seguimiento
        const isTradeSignal = !isFollowUpImage &&
                             data.response.includes('Activo:') &&
                             data.response.includes('Dirección:') &&
                             data.response.includes('Entrada:') &&
                             data.response.includes('Stop Loss:');

        if (isTradeSignal) {
          // Extraer información del trade
          const lines = data.response.split('\n');
          const tradeInfo = {};

          lines.forEach(line => {
            if (line.includes('Activo:')) tradeInfo.asset = line.split(':')[1]?.trim();
            if (line.includes('Dirección:')) tradeInfo.direction = line.split(':')[1]?.trim();
            if (line.includes('Entrada:')) tradeInfo.entry = line.split(':')[1]?.trim();
            if (line.includes('Stop Loss:')) tradeInfo.stopLoss = line.split(':')[1]?.trim();
          });

          // Iniciar seguimiento automático
          if (tradeInfo.asset && tradeInfo.direction) {
            startTradeFollowUp(tradeInfo);

            // Mensaje de confirmación de seguimiento
            setTimeout(() => {
              const followUpConfirmation = {
                role: "assistant",
                content: `✅ **SEGUIMIENTO ACTIVADO**

🎯 Estaré pendiente de tu trade **${tradeInfo.asset} ${tradeInfo.direction}**

⏰ **CADA 30 MINUTOS** te pediré una captura para decidir:
• 🟡 **Break Even**: Si mover Stop Loss
• 🟢 **Take Profits**: Si tomar ganancias parciales
• 🔴 **Stop Loss**: Si el trade terminó
• 🚀 **Dejar correr**: Si va hacia TP4

💋 **CONTINUARÉ HASTA:**
• Que alcances TP4 o Stop Loss
• Que presiones "✅ Finalizar" en el panel
• Que yo detecte que el trade terminó

¡Mantente atento a mis notificaciones cada 30 minutos! 🔔`
              };
              setMessages(prev => [...prev, followUpConfirmation]);
            }, 2000);
          }
        }

        // Detectar si Germayori indica que el trade terminó
        const tradeFinished = data.response.includes('TP4 alcanzado') ||
                             data.response.includes('Stop Loss activado') ||
                             data.response.includes('trade terminado') ||
                             data.response.includes('operación cerrada') ||
                             data.response.includes('CERRAR OPERACIÓN');

        if (tradeFinished && currentMessage.includes('OP-')) {
          // Extraer ID de operación del mensaje del usuario
          const operationMatch = currentMessage.match(/OP-\d+/);
          if (operationMatch) {
            const operationId = operationMatch[0];
            setTimeout(() => {
              const finishMessage = {
                role: "assistant",
                content: `🎯 **OPERACIÓN ${operationId} COMPLETADA**

✅ El trade ha llegado a su conclusión natural.

📊 **SEGUIMIENTO FINALIZADO**
Ya no recibirás más notificaciones de esta operación.

💋 ¿Listo para una nueva operación? Sube 5 pantallas cuando quieras. 🚀`
              };
              setMessages(prev => [...prev, finishMessage]);
              closeOperation(operationId, 'trade_completed');
            }, 1000);
          }
        }

        // Solo hablar si el mensaje original fue por voz
        if (wasVoiceMessage) {
          speak(data.response);
        }
      } else {
        const errorMessage = {
          role: "assistant",
          content: "Lo siento, hubo un problema procesando tu mensaje. 🔧"
        };
        setMessages(prev => [...prev, errorMessage]);
      }
    } catch (error) {
      const errorMessage = {
        role: "assistant",
        content: "Error de conexión. Intenta de nuevo. 🌐"
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const startChat = () => {
    setInputMessage("Hola Germayori, ¿estás lista para ayudarme con trading?");
    setTimeout(() => {
      handleSendMessage();
    }, 100);
  };

  // Función para que Germayori hable
  const speak = (text) => {
    if ('speechSynthesis' in window) {
      // Cancelar cualquier síntesis anterior
      speechSynthesis.cancel();

      const utterance = new SpeechSynthesisUtterance(text);

      // Esperar a que las voces estén cargadas
      const setVoice = () => {
        const voices = speechSynthesis.getVoices();
        console.log("Voces disponibles:", voices.map(v => `${v.name} (${v.lang})`));

        // Buscar voz en español
        const spanishVoice = voices.find(v =>
          v.lang.includes("es") && (v.name.includes("Google") || v.name.includes("Microsoft"))
        ) || voices.find(v => v.lang.includes("es"));

        if (spanishVoice) {
          utterance.voice = spanishVoice;
          console.log("Voz seleccionada:", spanishVoice.name);
        } else {
          console.log("No se encontró voz en español, usando voz por defecto");
        }

        utterance.rate = 0.9;
        utterance.pitch = 1.2; // Voz más femenina
        utterance.volume = 0.8;

        utterance.onstart = () => console.log("Iniciando síntesis de voz");
        utterance.onend = () => console.log("Síntesis de voz completada");
        utterance.onerror = (e) => console.error("Error en síntesis de voz:", e);

        speechSynthesis.speak(utterance);
      };

      // Si las voces ya están cargadas
      if (speechSynthesis.getVoices().length > 0) {
        setVoice();
      } else {
        // Esperar a que se carguen las voces
        speechSynthesis.onvoiceschanged = setVoice;
      }
    } else {
      console.error("speechSynthesis no está disponible en este navegador");
    }
  };

  // Función para reconocimiento de voz
  const startRecognition = () => {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    if (!SpeechRecognition) {
      alert("Tu navegador no soporta reconocimiento de voz.");
      return;
    }

    const recognition = new SpeechRecognition();
    recognition.lang = "es-ES";
    recognition.interimResults = false;
    recognition.maxAlternatives = 1;

    recognition.onresult = (event) => {
      const speechToText = event.results[0][0].transcript;
      setInputMessage(speechToText);
      setIsVoiceMessage(true); // Marcar que es un mensaje de voz
      // Auto enviar el mensaje después de reconocer la voz
      setTimeout(() => {
        handleSendMessage();
      }, 500);
    };

    recognition.onerror = (event) => {
      console.error('Error de reconocimiento:', event.error);
      setRecording(false);
    };

    recognition.onend = () => {
      setRecording(false);
    };

    recognitionRef.current = recognition;
    recognition.start();
    setRecording(true);
  };



  return (
    <PrivateRoute>
      <div className="min-h-screen bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700">
        {/* Header Superior */}
        <div className="bg-black/30 backdrop-blur-sm border-b border-white/10">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* Botón Regresar a Inicio */}
                <button
                  onClick={() => window.location.href = '/'}
                  className="flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 transition-all duration-200 text-secondary-200 hover:text-secondary-100"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  <span className="text-sm font-medium">Regresar a Inicio</span>
                </button>

                <div className="w-12 h-12 bg-gradient-to-r from-secondary-400 to-secondary-600 rounded-full flex items-center justify-center">
                  <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-secondary-400">Germayori AI</h1>
                  <p className="text-secondary-200 text-sm">Estrategia Pro - Liquidez Institucional</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-secondary-200 text-sm">Usuario: {session?.user?.name || session?.user?.email}</p>
                <div className="flex items-center space-x-2 mt-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-xs font-medium">En línea</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Contenedor Principal */}
        <div className="flex flex-col h-[calc(100vh-80px)]">

          {/* Panel de seguimiento individual - COMPACTO para no tapar análisis */}
          {activeTradesFollowUp.filter(op => op.status === 'active').length > 0 && (
            <div className="p-3 pb-0">
              <div className="max-w-6xl mx-auto">
                <div className="bg-secondary-500/20 border border-secondary-500/50 rounded-lg p-3 mb-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">📸</span>
                      <h3 className="text-secondary-400 font-bold text-sm">
                        Seguimiento Activo
                      </h3>
                    </div>
                    <p className="text-secondary-400 text-xs">
                      Sube imagen para análisis
                    </p>
                  </div>

                  {/* Mostrar operaciones activas de forma compacta */}
                  <div className="flex flex-wrap gap-2 mb-2">
                    {activeTradesFollowUp.filter(op => op.status === 'active').map(operation => (
                      <div key={operation.id} className="bg-primary-600/20 rounded px-2 py-1 text-xs">
                        <span className="text-secondary-400 font-semibold">
                          {operation.id}: {operation.asset} {operation.direction}
                        </span>
                        <span className="text-secondary-300 ml-1">
                          | E: {operation.entry}
                        </span>
                      </div>
                    ))}
                  </div>

                  {/* Área de subida compacta */}
                  <div className="border border-dashed border-secondary-500/50 rounded p-2 bg-primary-600/10">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={async (e) => {
                        const file = e.target.files[0];
                        if (file) {
                          try {
                            setSelectedImage(file);
                            const compressedImage = await compressImage(file);
                            setImagePreview(compressedImage);
                            console.log(`✅ Imagen de seguimiento comprimida exitosamente`);
                          } catch (error) {
                            console.error('Error comprimiendo imagen de seguimiento:', error);
                            // Fallback al método original
                            const reader = new FileReader();
                            reader.onload = (e) => {
                              setSelectedImage(file);
                              setImagePreview(e.target.result);
                            };
                            reader.readAsDataURL(file);
                          }
                        }
                      }}
                      className="hidden"
                      id="followup-image-upload"
                    />
                    <label
                      htmlFor="followup-image-upload"
                      className="cursor-pointer flex items-center justify-center space-x-2"
                    >
                      <span className="text-lg">📷</span>
                      <span className="text-secondary-400 text-xs font-medium">
                        Subir gráfico
                      </span>
                    </label>

                    {imagePreview && (
                      <div className="mt-2 flex items-center justify-between">
                        <img
                          src={imagePreview}
                          alt="Vista previa"
                          className="h-6 w-8 object-contain rounded border border-secondary-500/30"
                        />
                        <button
                          onClick={() => {
                            setSelectedImage(null);
                            setImagePreview(null);
                          }}
                          className="text-red-400 text-xs hover:text-red-300"
                        >
                          ❌
                        </button>
                      </div>
                    )}
                  </div>

                  {selectedImage && (
                    <button
                      onClick={() => {
                        setInputMessage("SEGUIMIENTO: Analiza el progreso de la operación activa con esta imagen");
                        handleSendMessage();
                      }}
                      className="mt-1 bg-secondary-500 hover:bg-secondary-600 text-white px-3 py-1 rounded text-xs font-medium transition-colors"
                    >
                      🔍 Analizar
                    </button>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Panel Multi-Timeframe - SIEMPRE VISIBLE cuando hay mensajes */}
          {messages.length > 0 && (
            <div className="p-6 pb-0">
              <div className="max-w-6xl mx-auto">



                {/* Panel de análisis multi-timeframe - Solo aparece cuando NO hay operaciones activas */}
                {activeTradesFollowUp.filter(op => op.status === 'active').length === 0 && (
                  <div className="bg-black/40 border border-white/20 rounded-xl p-6 mb-6">
                    <h3 className="text-secondary-400 font-bold text-xl mb-4 text-center">
                      📊 Análisis Multi-Timeframe - Sube las 5 Imágenes
                    </h3>

                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
                    {['D1', 'H4', 'H1', 'M30', 'M5'].map((timeframe) => (
                      <div key={timeframe} className="bg-black/40 border border-white/20 rounded-lg p-4">
                        <h4 className="text-secondary-400 font-semibold text-center mb-3">{timeframe}</h4>

                        {timeframeImages[timeframe].preview ? (
                          <div className="relative">
                            <img
                              src={timeframeImages[timeframe].preview}
                              alt={`Gráfico ${timeframe}`}
                              className="w-full h-32 object-cover rounded-lg border border-white/30"
                            />
                            <button
                              onClick={() => removeTimeframeImage(timeframe)}
                              className="absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm"
                            >
                              ×
                            </button>
                          </div>
                        ) : (
                          <label className="cursor-pointer">
                            <div className="w-full h-32 border-2 border-dashed border-secondary-400/50 rounded-lg flex flex-col items-center justify-center hover:border-secondary-400 transition-colors">
                              <svg className="w-8 h-8 text-secondary-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                              </svg>
                              <span className="text-secondary-300 text-sm">Subir {timeframe}</span>
                            </div>
                            <input
                              type="file"
                              accept="image/*"
                              onChange={(e) => handleTimeframeImageUpload(timeframe, e)}
                              className="hidden"
                            />
                          </label>
                        )}
                      </div>
                    ))}
                  </div>

                  {allImagesUploaded && (
                    <div className="text-center">
                      <button
                        onClick={handleAnalyzeMultiTimeframe}
                        disabled={isLoading}
                        className="bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 disabled:opacity-50 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300"
                      >
                        {isLoading ? "Analizando..." : "🚀 Analizar Multi-Timeframe"}
                      </button>
                    </div>
                  )}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Área de Mensajes */}
          <div className="flex-1 overflow-y-auto p-6 custom-scrollbar">
            <div className="max-w-6xl mx-auto">
              {messages.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full min-h-[500px]">
                  <div className="bg-gradient-to-r from-secondary-500/20 to-primary-500/20 rounded-3xl p-12 border border-white/10 max-w-4xl">
                    <div className="text-center">
                      <div className="w-24 h-24 bg-gradient-to-r from-secondary-500 to-secondary-600 rounded-full flex items-center justify-center mx-auto mb-8">
                        <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                      </div>
                      <h2 className="text-4xl font-bold text-secondary-400 mb-6">¡Bienvenido a Germayori AI!</h2>
                      <p className="text-secondary-200 text-xl leading-relaxed mb-8">
                        Tu mentora especializada en <span className="text-secondary-400 font-semibold">Smart Money Concepts</span><br/>
                        Creadora de la <span className="text-secondary-400 font-semibold">✨ Estrategia Germayori Pro ✨</span>
                      </p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <div className="bg-white/10 rounded-xl p-6 border border-white/20">
                          <h3 className="text-secondary-400 font-bold text-lg mb-3">📊 Análisis Técnico</h3>
                          <p className="text-secondary-200">FVG, Order Blocks, CHoCH/BOS, Liquidez Institucional</p>
                        </div>
                        <div className="bg-white/10 rounded-xl p-6 border border-white/20">
                          <h3 className="text-secondary-400 font-bold text-lg mb-3">📈 Análisis de Gráficos</h3>
                          <p className="text-secondary-200">Sube imágenes de gráficos para análisis detallado</p>
                        </div>
                        <div className="bg-white/10 rounded-xl p-6 border border-white/20">
                          <h3 className="text-secondary-400 font-bold text-lg mb-3">⚡ Señales en Tiempo Real</h3>
                          <p className="text-secondary-200">Identificación de oportunidades de compra y venta</p>
                        </div>
                        <div className="bg-white/10 rounded-xl p-6 border border-white/20">
                          <h3 className="text-secondary-400 font-bold text-lg mb-3">🎯 Multi-Timeframe</h3>
                          <p className="text-secondary-200">Análisis en Daily, H1, M30 y M5</p>
                        </div>
                      </div>

                      <button
                        onClick={startChat}
                        className="bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 text-white px-8 py-4 rounded-xl text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg"
                      >
                        🚀 Comenzar Chat con Germayori
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-6">
                  {messages.map((message, index) => (
                    <div
                      key={index}
                      className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
                    >
                      <div
                        className={`max-w-2xl px-6 py-4 rounded-2xl ${
                          message.role === "user"
                            ? message.hasMultiTimeframe
                              ? "bg-black/40 border border-white/20 text-secondary-100"
                              : "bg-gradient-to-r from-secondary-500 to-secondary-600 text-white"
                            : "bg-white/10 text-secondary-100 border border-white/20"
                        }`}
                      >
                        {message.hasImage && message.image && (
                          <div className="mb-4">
                            <img
                              src={message.image}
                              alt="Imagen enviada"
                              className="max-w-full max-h-96 rounded-lg border border-white/30"
                            />
                          </div>
                        )}
                        {message.hasMultiTimeframe && message.multiTimeframeImages && (
                          <div className="mb-4">
                            <div className="text-secondary-300 text-sm mb-2">📊 Análisis Multi-Timeframe:</div>
                            <div className="grid grid-cols-5 gap-2">
                              {['D1', 'H4', 'H1', 'M30', 'M5'].map(timeframe => (
                                message.multiTimeframeImages[timeframe] && (
                                  <div key={timeframe} className="text-center">
                                    <div className="text-secondary-400 text-xs mb-1">{timeframe}</div>
                                    <img
                                      src={message.multiTimeframeImages[timeframe]}
                                      alt={`Gráfico ${timeframe}`}
                                      className="w-full h-20 object-cover rounded border border-white/30"
                                    />
                                  </div>
                                )
                              ))}
                            </div>
                          </div>
                        )}
                        {!message.hasMultiTimeframe && (
                          <div className="whitespace-pre-wrap">{message.content}</div>
                        )}
                      </div>
                    </div>
                  ))}
                  {isLoading && (
                    <div className="flex justify-start">
                      <div className="bg-white/10 border border-white/20 rounded-2xl px-6 py-4">
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-secondary-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-secondary-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                          <div className="w-2 h-2 bg-secondary-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                          <span className="text-secondary-200 ml-2">Germayori está pensando...</span>
                        </div>
                      </div>
                    </div>
                  )}
                  <div ref={messagesEndRef} />
                </div>
              )}
            </div>
          </div>

          {/* Área de Input */}
          <div className="bg-black/30 backdrop-blur-sm border-t border-white/10 p-6">
            <div className="max-w-6xl mx-auto">
              <div className="flex flex-col space-y-4">
                {/* Preview de imagen */}
                {imagePreview && (
                  <div className="flex items-center space-x-4 bg-white/10 rounded-xl p-4 border border-white/20">
                    <img src={imagePreview} alt="Preview" className="w-16 h-16 rounded-lg object-cover" />
                    <span className="text-secondary-200 flex-1">Imagen seleccionada</span>
                    <button
                      onClick={removeImage}
                      className="text-red-400 hover:text-red-300 transition-colors"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                )}

                <div className="flex space-x-4">
                  <div className="flex-1">
                    <textarea
                      value={inputMessage}
                      onChange={(e) => setInputMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Pregunta sobre trading, análisis técnico o sube una imagen de gráfico..."
                      className="w-full bg-white/10 border border-white/20 rounded-xl px-6 py-4 text-secondary-100 placeholder-secondary-300 focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:border-transparent resize-none"
                      rows="3"
                      disabled={isLoading}
                    />
                  </div>
                  
                  <div className="flex flex-col space-y-2">
                    {/* Botón subir imagen */}
                    <label className="bg-white/10 hover:bg-white/20 border border-white/20 text-secondary-200 px-4 py-2 rounded-xl cursor-pointer transition-colors duration-200 flex items-center justify-center">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                        disabled={isLoading}
                      />
                    </label>

                    {/* Botón de micrófono */}
                    <button
                      onClick={startRecognition}
                      disabled={isLoading || recording}
                      className={`px-4 py-2 rounded-xl transition-colors duration-200 flex items-center justify-center ${
                        recording
                          ? 'bg-red-500 hover:bg-red-600 text-white animate-pulse'
                          : 'bg-white/10 hover:bg-white/20 border border-white/20 text-secondary-200'
                      }`}
                    >
                      {recording ? (
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                          <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                        </svg>
                      ) : (
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                        </svg>
                      )}
                    </button>

                    {/* Botón enviar */}
                    <button
                      onClick={handleSendMessage}
                      disabled={isLoading || (!inputMessage.trim() && !selectedImage)}
                      className="bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 disabled:from-gray-600 disabled:to-gray-700 disabled:opacity-50 text-white px-4 py-2 rounded-xl font-medium transition-all duration-200 flex items-center justify-center"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                      </svg>
                    </button>
                  </div>
                </div>

                {/* Indicador de grabación */}
                {recording && (
                  <div className="mt-4 flex items-center justify-center space-x-2 text-red-400">
                    <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                    <span className="text-sm font-medium">🎧 Escuchando... Habla ahora</span>
                  </div>
                )}

                {/* Indicador de múltiples operaciones activas */}
                {activeTradesFollowUp.filter(op => op.status === 'active').length > 0 && (
                  <div className="mt-4 space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="text-secondary-400 font-semibold text-sm">
                        🎯 Operaciones Activas ({activeTradesFollowUp.filter(op => op.status === 'active').length})
                      </h4>
                      <button
                        onClick={() => setShowOperationsPanel(!showOperationsPanel)}
                        className="text-secondary-300 hover:text-white transition-colors text-xs"
                      >
                        {showOperationsPanel ? 'Ocultar' : 'Ver todas'}
                      </button>
                    </div>

                    {activeTradesFollowUp
                      .filter(op => op.status === 'active')
                      .slice(0, showOperationsPanel ? undefined : 2)
                      .map(operation => (
                        <div key={operation.id} className="bg-secondary-500/20 border border-secondary-500/30 rounded-xl p-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className="w-2 h-2 bg-secondary-500 rounded-full animate-pulse"></div>
                              <div>
                                <p className="text-secondary-400 font-semibold text-xs">
                                  {operation.id}: {operation.asset} {operation.direction}
                                </p>
                                <p className="text-secondary-300 text-xs">
                                  Seguimientos: {operation.followUpCount} | Entrada: {operation.entry}
                                </p>
                              </div>
                            </div>
                            <div className="flex space-x-2">
                              <button
                                onClick={() => {
                                  const confirmMessage = {
                                    role: "assistant",
                                    content: `✅ **OPERACIÓN ${operation.id} FINALIZADA**

🎯 **${operation.asset} ${operation.direction}** - Seguimiento terminado

📊 **RESUMEN:**
• Entrada: ${operation.entry}
• Seguimientos realizados: ${operation.followUpCount}
• Tiempo activo: ${Math.round((new Date() - new Date(operation.startTime)) / (1000 * 60))} minutos

💋 **¡Excelente trabajo!**
Ya no recibirás más notificaciones de esta operación.

¿Listo para una nueva operación? Sube 5 pantallas cuando quieras. 🚀`
                                  };
                                  setMessages(prev => [...prev, confirmMessage]);
                                  closeOperation(operation.id, 'user_finished');
                                }}
                                className="bg-green-500/20 hover:bg-green-500/30 text-green-400 px-3 py-1 rounded-lg text-xs font-medium transition-colors"
                                title="Finalizar operación"
                              >
                                ✅ Finalizar
                              </button>
                              <button
                                onClick={() => closeOperation(operation.id, 'manual')}
                                className="text-secondary-300 hover:text-red-400 transition-colors"
                                title="Cancelar operación"
                              >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                              </button>
                            </div>
                          </div>
                        </div>
                      ))
                    }
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <style jsx global>{`
          .custom-scrollbar::-webkit-scrollbar {
            width: 8px;
          }
          .custom-scrollbar::-webkit-scrollbar-track {
            background: rgba(139, 92, 246, 0.1);
            border-radius: 4px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(251, 191, 36, 0.6);
            border-radius: 4px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(251, 191, 36, 0.8);
          }

          /* Ocultar widgets externos completamente */
          iframe[src*="tradingview"],
          iframe[src*="widget"],
          div[id*="tradingview"],
          div[class*="tradingview"],
          .tradingview-widget-container,
          [data-tradingview-widget],
          script[src*="tradingview"] + div,
          div[style*="position: fixed"][style*="bottom"],
          div[style*="position: absolute"][style*="bottom"] {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            pointer-events: none !important;
          }
        `}</style>
      </div>
    </PrivateRoute>
  );
};

export default GermayoriPage;
