import React, { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/router";
import Head from "next/head";
import PrivateRoute from "../components/privateRoute";

const CanalVivo = () => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [signals, setSignals] = useState([]);
  const [isViewer, setIsViewer] = useState(false);
  const [newSignal, setNewSignal] = useState({
    asset: '',
    direction: '',
    entry: '',
    stopLoss: '',
    tp1: '',
    tp2: '',
    tp3: '',
    tp4: '',
    analysis: ''
  });

  const AUTHORIZED_EMAIL = "<EMAIL>";

  useEffect(() => {
    if (status === "loading") return;
    
    if (!session) {
      router.push('/login');
      return;
    }

    if (session.user.email === AUTHORIZED_EMAIL) {
      setIsAuthorized(true);
      setIsViewer(false);
    } else {
      setIsAuthorized(false);
      setIsViewer(true);
    }

    loadSignals();
    
    if (session.user.email === AUTHORIZED_EMAIL) {
      setTimeout(() => {
        initTradingView();
      }, 3000);
    }
  }, [session, status, router]);

  const loadSignals = async () => {
    try {
      const response = await fetch('/api/canal-vivo/signals');
      if (response.ok) {
        const data = await response.json();
        setSignals(data.signals || []);
      }
    } catch (error) {
      console.error('Error loading signals:', error);
    }
  };

  const initTradingView = () => {
    if (typeof window !== 'undefined') {
      const container = document.getElementById('tradingview_widget');
      if (container) {
        container.innerHTML = '';
      }

      if (window.TradingView) {
        try {
          new window.TradingView.widget({
            "width": "100%",
            "height": "600",
            "symbol": "OANDA:XAUUSD",
            "interval": "15",
            "timezone": "America/Panama",
            "theme": "dark",
            "style": "1",
            "locale": "es",
            "toolbar_bg": "#1a1a1a",
            "enable_publishing": false,
            "hide_top_toolbar": false,
            "hide_legend": false,
            "save_image": false,
            "container_id": "tradingview_widget",
            "allow_symbol_change": true,
            "studies": ["Volume@tv-basicstudies", "RSI@tv-basicstudies"]
          });
        } catch (error) {
          console.error('Error loading TradingView:', error);
          if (container) {
            container.innerHTML = '<div class="flex items-center justify-center h-full text-secondary-400"><p>Error cargando TradingView. Recarga la página.</p></div>';
          }
        }
      } else {
        console.log('TradingView no disponible, reintentando...');
        setTimeout(initTradingView, 1000);
      }
    }
  };

  const handleSendSignal = async () => {
    if (!newSignal.asset || !newSignal.direction || !newSignal.entry) {
      alert('Por favor completa los campos obligatorios: Activo, Dirección y Entrada');
      return;
    }

    try {
      const response = await fetch('/api/canal-vivo/signals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          asset: newSignal.asset,
          direction: newSignal.direction,
          entry: newSignal.entry,
          stopLoss: newSignal.stopLoss,
          takeProfit1: newSignal.tp1,
          takeProfit2: newSignal.tp2,
          takeProfit3: newSignal.tp3,
          takeProfit4: newSignal.tp4,
          analysis: newSignal.analysis
        })
      });

      if (response.ok) {
        const data = await response.json();
        setSignals([data.signal, ...signals]);
        
        // Limpiar formulario
        setNewSignal({
          asset: '',
          direction: '',
          entry: '',
          stopLoss: '',
          tp1: '',
          tp2: '',
          tp3: '',
          tp4: '',
          analysis: ''
        });
        
        alert('Señal enviada exitosamente');
      } else {
        const error = await response.json();
        alert(`Error: ${error.error}`);
      }
    } catch (error) {
      console.error('Error:', error);
      alert('Error al enviar la señal');
    }
  };

  const handleDeleteSignal = async (signalId) => {
    if (!confirm('¿Estás seguro de eliminar esta señal?')) return;

    try {
      const response = await fetch(`/api/canal-vivo/signals?id=${signalId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        setSignals(signals.filter(signal => signal._id !== signalId));
        alert('Señal eliminada exitosamente');
      } else {
        const error = await response.json();
        alert(`Error: ${error.error}`);
      }
    } catch (error) {
      console.error('Error:', error);
      alert('Error al eliminar la señal');
    }
  };

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 flex items-center justify-center">
        <div className="text-secondary-400 text-2xl">Cargando...</div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 flex items-center justify-center">
        <div className="text-center">
          <div className="text-secondary-400 text-2xl mb-4">Debes iniciar sesion</div>
          <button 
            onClick={() => router.push('/login')}
            className="bg-secondary-500 hover:bg-secondary-600 text-white px-6 py-3 rounded-lg"
          >
            Ir a Login
          </button>
        </div>
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Canal en Vivo - Analisis de Mercado</title>
        {isAuthorized && (
          <script
            src="https://s3.tradingview.com/tv.js"
            async
            onLoad={() => console.log('TradingView script loaded')}
          ></script>
        )}
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700">
        <div className="container mx-auto px-4 py-8">
          {/* Botón de regreso */}
          <div className="mb-6">
            <button
              onClick={() => router.push('/')}
              className="bg-secondary-500 hover:bg-secondary-600 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
              <span>Volver al Inicio</span>
            </button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            {isAuthorized && (
              <div className="lg:col-span-2">
                <div className="bg-black/40 backdrop-blur-sm border border-secondary-500/30 rounded-2xl p-6">
                  <div id="tradingview_widget" className="h-96"></div>
                </div>
              </div>
            )}

            <div className={isAuthorized ? "lg:col-span-3" : "lg:col-span-3"}>
              <div className="mt-8 bg-black/40 backdrop-blur-sm border border-secondary-500/30 rounded-2xl p-6">
                <h3 className="text-secondary-400 font-bold text-xl mb-4">
                  {isViewer ? "SEÑALES DEL CANAL" : "SEÑALES ENVIADAS"}
                </h3>
                
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {signals.length === 0 ? (
                    <div className="text-center text-secondary-400 py-8">
                      No hay señales disponibles
                    </div>
                  ) : (
                    signals.map((signal) => (
                      <div key={signal._id} className="bg-primary-800/50 rounded-lg p-4 border border-secondary-500/30">
                        <div className="flex justify-between items-start mb-2">
                          <div className="flex items-center space-x-2">
                            <span className="text-secondary-400 font-bold text-lg">{signal.asset}</span>
                            <span className={`px-2 py-1 rounded text-sm font-bold ${
                              signal.direction === 'BUY' ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
                            }`}>
                              {signal.direction}
                            </span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-secondary-500 text-sm">{new Date(signal.timestamp).toLocaleString()}</span>
                            {isAuthorized && (
                              <button
                                onClick={() => handleDeleteSignal(signal._id)}
                                className="text-red-400 hover:text-red-300 text-sm px-2 py-1 rounded hover:bg-red-600/20 transition-colors"
                                title="Eliminar señal"
                              >
                                🗑️
                              </button>
                            )}
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-6 gap-2 text-sm">
                          <div>
                            <span className="text-blue-300">Entrada: </span>
                            <span className="text-white">{signal.entry}</span>
                          </div>
                          {signal.stopLoss && (
                            <div>
                              <span className="text-red-300">SL: </span>
                              <span className="text-white">{signal.stopLoss}</span>
                            </div>
                          )}
                          {signal.takeProfit1 && (
                            <div>
                              <span className="text-green-300">TP1: </span>
                              <span className="text-white">{signal.takeProfit1}</span>
                            </div>
                          )}
                          {signal.takeProfit2 && (
                            <div>
                              <span className="text-green-300">TP2: </span>
                              <span className="text-white">{signal.takeProfit2}</span>
                            </div>
                          )}
                          {signal.takeProfit3 && (
                            <div>
                              <span className="text-green-300">TP3: </span>
                              <span className="text-white">{signal.takeProfit3}</span>
                            </div>
                          )}
                          {signal.takeProfit4 && (
                            <div>
                              <span className="text-green-300">TP4: </span>
                              <span className="text-white">{signal.takeProfit4}</span>
                            </div>
                          )}
                        </div>
                        
                        {signal.analysis && (
                          <div className="mt-3 p-3 bg-primary-700/30 rounded-lg border-l-4 border-secondary-400">
                            <span className="text-secondary-300 font-medium text-sm">Análisis: </span>
                            <p className="text-white text-sm mt-1">{signal.analysis}</p>
                          </div>
                        )}
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>

            {isAuthorized && (
              <div className="lg:col-span-1">
                <div className="bg-black/40 backdrop-blur-sm border border-secondary-500/30 rounded-2xl p-6 sticky top-6">
                  <h3 className="text-secondary-400 font-bold text-2xl mb-6 text-center">
                    ENVIAR SEÑAL
                  </h3>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-secondary-300 text-sm font-medium mb-2">ACTIVO</label>
                      <input
                        type="text"
                        placeholder="Ej: XAUUSD, EURUSD, BTCUSD"
                        value={newSignal.asset}
                        onChange={(e) => setNewSignal({...newSignal, asset: e.target.value.toUpperCase()})}
                        className="w-full bg-primary-600/50 border border-secondary-500/50 rounded-lg px-4 py-3 text-white text-center font-bold text-lg focus:border-secondary-400 focus:outline-none"
                      />
                    </div>

                    <div>
                      <label className="block text-secondary-300 text-sm font-medium mb-2">DIRECCION</label>
                      <select
                        value={newSignal.direction}
                        onChange={(e) => setNewSignal({...newSignal, direction: e.target.value})}
                        className="w-full bg-primary-600/50 border border-secondary-500/50 rounded-lg px-4 py-3 text-white text-center font-bold text-lg focus:border-secondary-400 focus:outline-none"
                      >
                        <option value="">Seleccionar</option>
                        <option value="BUY">COMPRA (BUY)</option>
                        <option value="SELL">VENTA (SELL)</option>
                      </select>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-blue-300 text-sm font-medium mb-2">ENTRADA</label>
                        <input
                          type="text"
                          placeholder="Precio"
                          value={newSignal.entry}
                          onChange={(e) => setNewSignal({...newSignal, entry: e.target.value})}
                          className="w-full bg-blue-600/20 border border-blue-500/50 rounded-lg px-3 py-2 text-white text-center focus:border-blue-400 focus:outline-none"
                        />
                      </div>
                      <div>
                        <label className="block text-red-300 text-sm font-medium mb-2">STOP LOSS</label>
                        <input
                          type="text"
                          placeholder="SL"
                          value={newSignal.stopLoss}
                          onChange={(e) => setNewSignal({...newSignal, stopLoss: e.target.value})}
                          className="w-full bg-red-600/20 border border-red-500/50 rounded-lg px-3 py-2 text-white text-center focus:border-red-400 focus:outline-none"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-green-300 text-sm font-medium mb-2">TAKE PROFITS</label>
                      <div className="grid grid-cols-2 gap-2">
                        <input
                          type="text"
                          placeholder="TP1"
                          value={newSignal.tp1}
                          onChange={(e) => setNewSignal({...newSignal, tp1: e.target.value})}
                          className="bg-green-600/20 border border-green-500/50 rounded-lg px-3 py-2 text-white text-center text-sm focus:border-green-400 focus:outline-none"
                        />
                        <input
                          type="text"
                          placeholder="TP2"
                          value={newSignal.tp2}
                          onChange={(e) => setNewSignal({...newSignal, tp2: e.target.value})}
                          className="bg-green-600/20 border border-green-500/50 rounded-lg px-3 py-2 text-white text-center text-sm focus:border-green-400 focus:outline-none"
                        />
                        <input
                          type="text"
                          placeholder="TP3"
                          value={newSignal.tp3}
                          onChange={(e) => setNewSignal({...newSignal, tp3: e.target.value})}
                          className="bg-green-600/20 border border-green-500/50 rounded-lg px-3 py-2 text-white text-center text-sm focus:border-green-400 focus:outline-none"
                        />
                        <input
                          type="text"
                          placeholder="TP4"
                          value={newSignal.tp4}
                          onChange={(e) => setNewSignal({...newSignal, tp4: e.target.value})}
                          className="bg-green-600/20 border border-green-500/50 rounded-lg px-3 py-2 text-white text-center text-sm focus:border-green-400 focus:outline-none"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-secondary-300 text-sm font-medium mb-2">ANALISIS</label>
                      <textarea
                        placeholder="Describe el analisis tecnico..."
                        value={newSignal.analysis}
                        onChange={(e) => setNewSignal({...newSignal, analysis: e.target.value})}
                        className="w-full bg-primary-600/50 border border-secondary-500/50 rounded-lg px-4 py-3 text-white text-sm h-24 resize-none focus:border-secondary-400 focus:outline-none"
                      />
                    </div>

                    <button
                      onClick={handleSendSignal}
                      className="w-full bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 text-white py-4 rounded-lg font-bold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg"
                    >
                      ENVIAR SEÑAL AL CANAL
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default function CanalVivoPage() {
  return (
    <PrivateRoute>
      <CanalVivo />
    </PrivateRoute>
  );
}
