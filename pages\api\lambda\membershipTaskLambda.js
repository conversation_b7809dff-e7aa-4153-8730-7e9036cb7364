import Client from "../../../models/userAcademyModel";
import connectDB from "../../../lib/mongoose";

const checkAndUpdateMembershipStatus = async () => {
  await connectDB();
  try {
    const currentDate = new Date();
    console.log(currentDate)
    const expiredClients = await Client.find({
      cutoffDate: { $lte: currentDate },
      canEnter: true,
    });

    for (const client of expiredClients) {
      client.canEnter = false;
      await client.save();
    }

    console.log(
      `Se han actualizado ${expiredClients.length} clientes con membresía vencida.`
    );
  } catch (error) {
    console.error("Error al actualizar clientes:", error);
  }
};

export default async function handler(req, res) {
  const { token } = req.body;
  try {
    if (req.method !== "PUT" && token !== "1234") {
      return res.status(405).json({ error: "Method not allowed" });
    }
    await checkAndUpdateMembershipStatus();
    res.status(200).json({"response" : "ok","status":200})
  } catch (error) {
    res.send(error, "error en checkout");
  }
}
