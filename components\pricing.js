import React, { useState, useEffect } from "react";
import { payWithYappy } from "../utils/PaymentGateway";
import { useRouter } from "next/router";
import { useSession } from "next-auth/react";
import { couponCode } from "../utils/couponConnect";

function Pricing() {
  const { data: session, status } = useSession();
  const pricing = {
    basico: {
      base: 800,
      descuento: 299
    },
    intermedio: {
      base: 499,
      descuento: 299
    },
    avanzado: {
      base: 699,
      descuento: 419
    },
  }
  const [showButtons, setShowButtons] = useState(false);
  const [discount, setDiscount] = useState();
  const [code, setCode] = useState();
  const [hoveredIndex, setHoveredIndex] = useState(null);
  const [modal, setModal] = useState(false);
  const [price, setPrice] = useState(false);
  const cardKey = [1, 2, 3];
  const router = useRouter();
  const currentRout = router.pathname;

  useEffect(() => {
    if (currentRout === "/membership") {
      setShowButtons(true);
    } else {
      setShowButtons(false);
    }
  }, [currentRout.pathname]);

  const handleCardHover = (index) => {
    setHoveredIndex(index);
    if (hoveredIndex !== 0) {
    }
  };
  const resetHover = () => {
    setHoveredIndex(null);
  };
  const activeModalPrice = (price) => {
    setModal(true);
    setPrice(price);
  };
  const pay = (price) => {
    setPrice(price);
    payWithYappy(price)
      .then(() => {
        console.log("Payment initiated");
      })
      .catch((error) => {
        console.error(error);
      });
  };
  const handlerChangePayment = () => {
    couponCode(code).then((response) =>
      response ? setDiscount(true) : setDiscount(false)
    );
  };
  return (
    <div className="w-full mx-auto  px-5 py-10 text-gray-600 dark:text-white mb-10">
      <div
        className={`max-w-5xl mx-auto md:flex
      ${
        modal
          ? "filter blur-md opacity-70 transition-all duration-300"
          : "transition-all duration-300"
      }`}
      >
        <div className="md:w-1/4 md:flex md:flex-col">
          <div className="text-left w-full flex-grow md:pr-5">
            <h1 className="text-4xl font-bold mb-5">Paquetes disponibles</h1>
            <h3 className="text-md font-medium mb-5">
              "Invierte en tu educación, que tu mente cultivada se encargará de
              llenar tus bolsillos."
            </h3>
          </div>
          <div className="w-full mb-2 flex flex-col gap-5 p-3">
            <p className="text-xs">Exitos En Tus Operaciones</p>
            <input
              className="text-xs h-10 mt-2 p-2 rounded-md bg-gray-200 text-black"
              placeholder="Descuento"
              onChange={(e) => {
                setCode(e.target.value.toLowerCase());
              }}
            ></input>
            <button
              onClick={() => handlerChangePayment()}
              className="font-bold bg-amber-500 h-10  hover:bg-gray-700 text-white rounded-md px-10 py-2 transition-colors"
            >
              Validar
            </button>
          </div>
        </div>
        <div className="md:w-3/4">
          <div className=" max-w-4xl mx-auto md:flex">
            <div
              key={cardKey[0]}
              onMouseEnter={(e) => handleCardHover(cardKey[0])}
              onMouseLeave={() => resetHover()}
              className={`w-full md:w-1/3 md:max-w-none bg-white dark:bg-black px-8 md:px-10 py-8 md:py-10 mb-3 mx-auto md:my-2 rounded-md shadow-lg shadow-gray-600 md:flex md:flex-col hover:scale-105 hover:z-50 duration-500 ease-in-out  ${
                hoveredIndex !== null && hoveredIndex !== cardKey[0]
                  ? "filter blur-md opacity-70 transition-all duration-300"
                  : ""
              }`}
            >
              <div className="w-full flex-grow">
                <h2 className="text-center font-bold uppercase mb-4">
                  CURSO COMPLETO
                </h2>
                <h3 className="text-center font-bold text-4xl mb-5">
                  {discount ? "$" + pricing.basico.descuento : "$" + pricing.basico.base}
                  <span className="text-sm">/4 meses</span>
                </h3>
                <ul className="text-sm mb-8">
                  <li className="leading-tight">
                    <i className="mdi mdi-check-bold text-lg"></i> Videos Básico,
                    Intermedio y Avanzado
                  </li>
                  <li className="leading-tight">
                    <i className="mdi mdi-check-bold text-lg"></i> Señales con la
                    Diosa del Trading Germayori
                  </li>
                  <li className="leading-tight">
                    <i className="mdi mdi-check-bold text-lg"></i> Mentoría una vez
                    por mes (4 sesiones)
                  </li>
                  <li className="leading-tight">
                    <i className="mdi mdi-check-bold text-lg"></i> Acceso completo
                    por 4 meses
                  </li>
                  <li className="leading-tight">
                    <i className="mdi mdi-check-bold text-lg text-amber-500"></i>
                    <span className="text-amber-500 font-semibold">BONUS:</span> Estrategia Germayori
                    Completa por $200 adicionales
                  </li>
                  <li className="leading-tight text-xs text-gray-400 mt-2">
                    * Al finalizar los 4 meses: 3 días de mentoría
                    intensiva y práctica con la estrategia completa
                  </li>
                </ul>
              </div>
              <div className="w-full">
                {showButtons === true ? (
                  <button
                    onClick={() => activeModalPrice(discount ? pricing.basico.descuento : pricing.basico.base)}
                    className="font-bold bg-amber-500 hover:bg-gray-700 text-white rounded-md px-10 py-2 transition-colors w-full"
                  >
                    Comprar
                  </button>
                ) : (
                  <></>
                )}
              </div>
            </div>
            {/* <div
              key={cardKey[1]}
              onMouseEnter={(e) => handleCardHover(cardKey[1])}
              onMouseLeave={() => resetHover()}
              className={`w-full md:w-1/3 md:max-w-none bg-white dark:bg-black px-8 md:px-10 py-8 md:py-10 mb-3 mx-auto md:my-2 rounded-md shadow-lg shadow-gray-600 md:flex md:flex-col hover:scale-105 hover:z-50 duration-500 ease-in-out  ${
                hoveredIndex !== null && hoveredIndex !== cardKey[1]
                  ? "filter blur-md opacity-70 transition-all duration-300"
                  : ""
              }`}
            >
              <div className="w-full flex-grow">
                <h2 className="text-center font-bold uppercase mb-4">
                  TRADING INTERMEDIO
                </h2>
                <h3 className="text-center font-bold text-4xl mb-5">
                  {discount ? "$" + pricing.intermedio.descuento : "$" + pricing.intermedio.base}
                  <span className="text-sm">/mensual</span>
                </h3>
                <ul className="text-sm mb-8">
                  <li className="leading-tight">
                    <i className="mdi mdi-check-bold text-lg"></i> Acceso a la
                    comunidad RP
                  </li>
                  <li className="leading-tight">
                    <i className="mdi mdi-check-bold text-lg"></i> Cursos
                    completos de todos los niveles
                  </li>
                  <li className="leading-tight">
                    <i className="mdi mdi-check-bold text-lg"></i> Mentoria 1 a
                    1
                  </li>
                  <li className="leading-tight">
                    <i className="mdi mdi-check-bold text-lg"></i> Guia de
                    gestion de cuenta auditada y fondeada
                  </li>
                  <li className="leading-tight">
                    <i className="mdi mdi-check-bold text-lg"></i> Paquete de
                    señales
                  </li>
                </ul>
              </div>
              <div className="w-full">
                {showButtons ? (
                  <button
                    onClick={() => activeModalPrice(discount ? pricing.intermedio.descuento : pricing.intermedio.base)}
                    className="font-bold bg-amber-500 hover:bg-gray-700 text-white rounded-md px-10 py-2 transition-colors w-full"
                  >
                    Comprar
                  </button>
                ) : (
                  <></>
                )}
              </div>
            </div> */}
            {/* <div
              key={cardKey[2]}
              onMouseEnter={(e) => handleCardHover(cardKey[2])}
              onMouseLeave={() => resetHover()}
              className={`w-full md:w-1/3 md:max-w-none bg-white dark:bg-black px-8 md:px-10 py-8 md:py-10 mb-3 mx-auto md:my-2 rounded-md shadow-lg shadow-gray-600 md:flex md:flex-col hover:scale-105 hover:z-50 duration-500 ease-in-out  ${
                hoveredIndex !== null && hoveredIndex !== cardKey[2]
                  ? "filter blur-md opacity-70 transition-all duration-300"
                  : ""
              }`}
            >
              <div className="w-full flex-grow">
                <h2 className="text-center font-bold uppercase mb-4">
                  MASTER TRADING
                </h2>
                <h3 className="text-center font-bold text-4xl mb-5">
                  {discount ? "$" + pricing.avanzado.descuento : "$" + pricing.avanzado.base}
                  <span className="text-sm">/mensual</span>
                </h3>
                <ul className="text-sm mb-8">
                  <li className="leading-tight">
                    <i className="mdi mdi-check-bold text-lg"></i> Acceso a la
                    comunidad RP
                  </li>
                  <li className="leading-tight">
                    <i className="mdi mdi-check-bold text-lg"></i> Cursos
                    completos de todos los niveles
                  </li>
                  <li className="leading-tight">
                    <i className="mdi mdi-check-bold text-lg"></i> Mentoria 1 a
                    1
                  </li>
                  <li className="leading-tight">
                    <i className="mdi mdi-check-bold text-lg"></i> Guia de
                    gestion de cuenta auditada y fondeada
                  </li>
                  <li className="leading-tight">
                    <i className="mdi mdi-check-bold text-lg"></i> Paquete de
                    señales
                  </li>
                  <li className="leading-tight">
                    <i className="mdi mdi-check-bold text-lg"></i> 
                    Estrategia Interbancaria RP
                  </li>
                </ul>
              </div>
              <div className="w-full">
                {showButtons ? (
                  <button
                    onClick={() => activeModalPrice(discount ? pricing.avanzado.descuento : pricing.avanzado.base)}
                    className="font-bold bg-amber-500 hover:bg-gray-700 text-white rounded-md px-10 py-2 transition-colors w-full"
                  >
                    Comprar
                  </button>
                ) : (
                  <></>
                )}
              </div>
            </div> */}
          </div>
        </div>
      </div>
      {modal && (
        <div className="fixed top-0 left-0 w-full h-full flex items-center justify-center bg-modal-background bg-cover bg-blur z-50">
          <div className="dark:bg-black bg-white p-8 rounded-lg shadow-gray-700 shadow-md">
            <p className="text-lg font-semibold mb-4">
              Selecciona un metodo de pago para continuar
            </p>
            <p className="text-lg font-semibold mb-4">
              Precio total: ${(price + price * 0.07).toFixed(2)}
            </p>
            <div className="flex justify-between">
              <button
                onClick={() => setModal(false)}
                className="bg-amber-500 text-white px-4 py-2 rounded hover:scale-105 duration-700 hover:bg-indigo-600"
              >
                Cerrar
              </button>
              <button
                onClick={() => pay(price)}
                className="bg-secondary-500 text-white px-4 py-2 rounded hover:scale-105 duration-700 hover:bg-primary-600"
              >
                Pagar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default Pricing;
