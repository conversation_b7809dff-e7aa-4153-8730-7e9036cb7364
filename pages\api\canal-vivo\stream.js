import { getServerSession } from "next-auth/next";
import { authOptions } from "../auth/[...nextauth].js";

// Estado del stream (en producción usar base de datos o Redis)
let streamState = {
  isLive: false,
  startTime: null,
  viewers: 0,
  title: 'Análisis de Mercado en Vivo',
  description: 'Canal exclusivo de análisis técnico y señales de trading'
};

export default async function handler(req, res) {
  const session = await getServerSession(req, res, authOptions);
  
  // Verificar autenticación
  if (!session) {
    return res.status(401).json({ error: 'No autenticado' });
  }

  const { method } = req;
  const AUTHORIZED_EMAIL = "<EMAIL>";

  try {
    switch (method) {
      case 'GET':
        // Obtener estado del stream
        res.status(200).json({ 
          stream: {
            ...streamState,
            currentTime: new Date().toISOString(),
            duration: streamState.isLive && streamState.startTime 
              ? Date.now() - new Date(streamState.startTime).getTime()
              : 0
          }
        });
        break;

      case 'POST':
        // Solo el analista autorizado puede controlar el stream
        if (session.user.email !== AUTHORIZED_EMAIL) {
          return res.status(403).json({ error: 'No autorizado para controlar el stream' });
        }

        const { action, title, description } = req.body;

        switch (action) {
          case 'start':
            if (streamState.isLive) {
              return res.status(400).json({ error: 'El stream ya está activo' });
            }
            
            streamState = {
              ...streamState,
              isLive: true,
              startTime: new Date().toISOString(),
              viewers: 0,
              title: title || streamState.title,
              description: description || streamState.description
            };

            res.status(200).json({ 
              message: 'Stream iniciado',
              stream: streamState 
            });
            break;

          case 'stop':
            if (!streamState.isLive) {
              return res.status(400).json({ error: 'El stream no está activo' });
            }

            streamState = {
              ...streamState,
              isLive: false,
              startTime: null,
              viewers: 0
            };

            res.status(200).json({ 
              message: 'Stream detenido',
              stream: streamState 
            });
            break;

          case 'update':
            streamState = {
              ...streamState,
              title: title || streamState.title,
              description: description || streamState.description
            };

            res.status(200).json({ 
              message: 'Stream actualizado',
              stream: streamState 
            });
            break;

          default:
            return res.status(400).json({ error: 'Acción no válida' });
        }
        break;

      case 'PUT':
        // Actualizar número de viewers (simulado)
        if (streamState.isLive) {
          const { viewerChange = 0 } = req.body;
          streamState.viewers = Math.max(0, streamState.viewers + viewerChange);
        }

        res.status(200).json({ 
          message: 'Viewers actualizados',
          viewers: streamState.viewers 
        });
        break;

      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Error en API de stream:', error);
    res.status(500).json({ 
      error: 'Error interno del servidor', 
      details: error.message 
    });
  }
}
