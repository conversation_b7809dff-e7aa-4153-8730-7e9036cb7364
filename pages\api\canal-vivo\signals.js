import connectDB from '../../../lib/mongoose.js';
import { getServerSession } from "next-auth/next";
import { authOptions } from "../auth/[...nextauth].js";
import mongoose from 'mongoose';

// Modelo de señales para MongoDB
const signalSchema = new mongoose.Schema({
  asset: { type: String, required: true },
  direction: { type: String, required: true },
  entry: { type: String, required: true },
  stopLoss: String,
  takeProfit1: String,
  takeProfit2: String,
  takeProfit3: String,
  takeProfit4: String,
  analysis: String,
  timestamp: { type: Date, default: Date.now },
  status: { type: String, default: 'active' },
  author: String
});

const Signal = mongoose.models.Signal || mongoose.model('Signal', signalSchema);

export default async function handler(req, res) {
  const session = await getServerSession(req, res, authOptions);
  
  if (!session) {
    return res.status(401).json({ error: 'No autenticado' });
  }

  const { method } = req;
  const AUTHORIZED_EMAIL = "<EMAIL>";

  try {
    await connectDB();

    switch (method) {
      case 'GET':
        const signals = await Signal.find().sort({ timestamp: -1 }).limit(50);
        res.status(200).json({ signals });
        break;

      case 'POST':
        if (session.user.email !== AUTHORIZED_EMAIL) {
          return res.status(403).json({ error: 'No autorizado para enviar señales' });
        }

        const {
          asset,
          direction,
          entry,
          stopLoss,
          takeProfit1,
          takeProfit2,
          takeProfit3,
          takeProfit4,
          analysis
        } = req.body;

        if (!asset || !direction || !entry) {
          return res.status(400).json({ 
            error: 'Campos obligatorios: asset, direction, entry' 
          });
        }

        const newSignal = new Signal({
          asset: asset.toUpperCase(),
          direction: direction.toUpperCase(),
          entry,
          stopLoss,
          takeProfit1,
          takeProfit2,
          takeProfit3,
          takeProfit4,
          analysis,
          author: session.user.name || 'Analista Principal'
        });

        await newSignal.save();

        res.status(201).json({ 
          message: 'Señal creada exitosamente',
          signal: newSignal 
        });
        break;

      case 'DELETE':
        if (session.user.email !== AUTHORIZED_EMAIL) {
          return res.status(403).json({ error: 'No autorizado' });
        }

        const { id } = req.query;
        await Signal.findByIdAndDelete(id);
        res.status(200).json({ message: 'Señal eliminada' });
        break;

      default:
        res.setHeader('Allow', ['GET', 'POST', 'DELETE']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Error en API de señales:', error);
    res.status(500).json({ 
      error: 'Error interno del servidor', 
      details: error.message 
    });
  }
}
