import Link from "next/link";
import ThemeChanger from "./DarkSwitch";
import Image from "next/image";
import { Disclosure } from "@headlessui/react";
import { AiFillTrademarkCircle } from "react-icons/ai";

const Navbar = () => {
  const navigation = [
    { route: "/", name: "Inicio" },
    { route: "videos", name: "Videos Academia" },
    { route: "ger<PERSON><PERSON><PERSON>", name: "Germa<PERSON>ri AI" },
    { route: "canal-vivo", name: "Canal en Vivo" },
    { route: "inversiones", name: "Inversiones" },
    { route: "noticias", name: "Noticias" },
    { route: "login", name: "Perfil" },
    { route: "membership", name: "Membre<PERSON><PERSON>" },
  ];

  return (
    <div className="w-full">
      <nav className="container relative flex flex-wrap items-center justify-between p-8 mx-auto lg:justify-between xl:px-0">
        <Disclosure>
          {({ open }) => (
            <>
              <div
                className={`flex flex-wrap items-center justify-between w-full lg:w-auto transition-opacity duration-1000 ease-in-out ${
                  open ? "opacity-100" : "opacity-50"
                }`}
              >
                <Link href="/">
                  <span className="flex items-center text-2xl font-medium text-secondary-500 dark:text-secondary-500">
                    <AiFillTrademarkCircle className="text-secondary-500 w-10 h-10 animate-pulse" />
                    <span>unning Pips</span>
                  </span>
                </Link>
                <Disclosure.Button
                  aria-label="Toggle Menu"
                  className="px-2 py-1 ml-auto text-secondary-500 rounded-md lg:hidden hover:text-secondary-500 focus:text-secondary-600 focus:bg-secondary-100 focus:outline-none dark:text-secondary-300 dark:focus:bg-secondary-700"
                >
                  <svg
                    className="w-6 h-6 fill-current"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                  >
                    {open && (
                      <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M18.278 16.864a1 1 0 0 1-1.414 1.414l-4.829-4.828-4.828 4.828a1 1 0 0 1-1.414-1.414l4.828-4.829-4.828-4.828a1 1 0 0 1 1.414-1.414l4.829 4.828 4.828-4.828a1 1 0 1 1 1.414 1.414l-4.828 4.829 4.828 4.828z"
                      />
                    )}
                    {!open && (
                      <path
                        fillRule="evenodd"
                        d="M4 5h16a1 1 0 0 1 0 2H4a1 1 0 1 1 0-2zm0 6h16a1 1 0 0 1 0 2H4a1 1 0 0 1 0-2zm0 6h16a1 1 0 0 1 0 2H4a1 1 0 0 1 0-2z"
                      />
                    )}
                  </svg>
                </Disclosure.Button>
                <Disclosure.Panel className="flex flex-wrap w-full my-5 lg:hidden">
                  <>
                    {navigation.map((item, index) => (
                      <Link
                        key={index}
                        href={item.route}
                        className="w-full px-4 py-2 -ml-4 text-gray-500 rounded-md dark:text-gray-300 hover:text-secondary-500 focus:text-secondary-500 focus:bg-secondary-100 dark:focus:bg-gray-800 focus:outline-none"
                      >
                        {item.name}
                      </Link>
                    ))}
                    <ThemeChanger />
                    <Link
                      href="/login"
                      className="w-full px-6 py-2 mt-3 text-center text-primary-900 bg-secondary-500 rounded-md lg:ml-5"
                    >
                      Empezar
                    </Link>
                  </>
                </Disclosure.Panel>
              </div>
            </>
          )}
        </Disclosure>

        {/* menu  */}
        <div className="hidden text-center lg:flex lg:items-center">
          <ul className="items-center justify-end flex-1 pt-6 list-none lg:pt-0 lg:flex">
            {navigation.map((menu, index) => (
              <li className="mr-3 nav__item" key={index}>
                <Link
                  href={menu.route}
                  className="inline-block px-4 py-2 text-lg font-normal text-gray-800 no-underline rounded-md dark:text-gray-200 hover:text-secondary-500 focus:text-secondary-500 focus:bg-secondary-100 focus:outline-none dark:focus:bg-gray-800"
                >
                  {menu.name}
                </Link>
              </li>
            ))}
          </ul>
        </div>

        <div className="hidden mr-3 space-x-4 lg:flex nav__item">
          <Link
            href="/login"
            className="px-6 py-2 text-primary-900 bg-secondary-500 rounded-md md:ml-5"
          >
            Empezar
          </Link>
          <ThemeChanger />
        </div>
      </nav>
    </div>
  );
};

export default Navbar;
