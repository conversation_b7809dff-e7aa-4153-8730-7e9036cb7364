# Agregar todos los archivos modificados
git add .

# Hacer commit con un mensaje descriptivo
git commit -m "feat: Agregar tabla de administración de usuarios y mejoras en canal vivo

- Agregar página de administración de usuarios (/admin/usuarios)
- Implementar activación/desactivación de usuarios (true/false)
- Agregar control manual de días de validación
- Mostrar días restantes y fecha de vencimiento
- Agregar TP3 y TP4 en señales del canal vivo
- Mostrar descripción/análisis en cada señal
- Mejorar interfaz de administración con tabla completa"

# Subir los cambios al repositorio
git push origin main