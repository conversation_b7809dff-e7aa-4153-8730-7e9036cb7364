import React, { useState, useEffect, useRef } from "react";
import PrivateRoute from "../components/privateRoute";
import ProgressMigration from "../components/ProgressMigration";
import { useSession } from "next-auth/react";
import { getSession } from "next-auth/react";

const VideosPage = () => {
  const { data: session } = useSession();
  const [videos, setVideos] = useState({});
  const [selectedVideo, setSelectedVideo] = useState(null);
  const [currentVideoName, setCurrentVideoName] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [playbackSpeed, setPlaybackSpeed] = useState(1);
  const [watchedVideos, setWatchedVideos] = useState(new Set());
  const [favoriteVideos, setFavoriteVideos] = useState(new Set());
  const [videoNotes, setVideoNotes] = useState({});
  const [showNotes, setShowNotes] = useState(false);
  const [currentNote, setCurrentNote] = useState("");
  const [progress, setProgress] = useState({});
  const videoRef = useRef(null);

  // Cargar datos del localStorage y de la base de datos
  useEffect(() => {
    const loadUserProgress = async () => {
      try {
        const session = await getSession();
        if (session?.user?.email) {
          // Cargar progreso real de la base de datos
          const response = await fetch(`/api/progress?userEmail=${session.user.email}`);
          const userProgressData = await response.json();

          if (userProgressData.videosWatched) {
            const watchedIds = userProgressData.videosWatched
              .filter(v => v.progress >= 80)
              .map(v => v.videoId);
            setWatchedVideos(new Set(watchedIds));
          }

          if (userProgressData.favoriteVideos) {
            setFavoriteVideos(new Set(userProgressData.favoriteVideos));
          }

          if (userProgressData.videoNotes) {
            const notesObj = {};
            userProgressData.videoNotes.forEach(note => {
              notesObj[note.videoId] = note.note;
            });
            setVideoNotes(notesObj);
          }

          // Cargar progreso de videos
          if (userProgressData.videosWatched) {
            const progressObj = {};
            userProgressData.videosWatched.forEach(video => {
              progressObj[video.videoId] = video.progress;
            });
            setProgress(progressObj);
          }
        }
      } catch (error) {
        console.error('Error loading user progress:', error);
        // Fallback a localStorage si hay error
        const savedWatched = localStorage.getItem('watchedVideos');
        const savedFavorites = localStorage.getItem('favoriteVideos');
        const savedNotes = localStorage.getItem('videoNotes');
        const savedProgress = localStorage.getItem('videoProgress');

        if (savedWatched) setWatchedVideos(new Set(JSON.parse(savedWatched)));
        if (savedFavorites) setFavoriteVideos(new Set(JSON.parse(savedFavorites)));
        if (savedNotes) setVideoNotes(JSON.parse(savedNotes));
        if (savedProgress) setProgress(JSON.parse(savedProgress));
      }
    };

    loadUserProgress();
  }, []);

  // Guardar en localStorage cuando cambian los datos
  useEffect(() => {
    localStorage.setItem('watchedVideos', JSON.stringify([...watchedVideos]));
  }, [watchedVideos]);

  useEffect(() => {
    localStorage.setItem('favoriteVideos', JSON.stringify([...favoriteVideos]));
  }, [favoriteVideos]);

  useEffect(() => {
    localStorage.setItem('videoNotes', JSON.stringify(videoNotes));
  }, [videoNotes]);

  useEffect(() => {
    localStorage.setItem('videoProgress', JSON.stringify(progress));
  }, [progress]);

  // Cargar videos
  useEffect(() => {
    const fetchVideos = async () => {
      try {
        const session = await getSession();
        const res = await fetch("/api/videos?user=" + session.user.email);
        const data = await res.json();

        // Filtrar solo videos (excluir PDFs y otras cosas)
        const filteredData = {};
        Object.keys(data).forEach(category => {
          // Solo incluir categorías que contengan "Trading" y excluir PDFs
          if (category.toLowerCase().includes('trading') && !category.toLowerCase().includes('pdf')) {
            const videoFiles = data[category].filter(file =>
              file.name.toLowerCase().endsWith('.mp4') ||
              file.name.toLowerCase().endsWith('.mov') ||
              file.name.toLowerCase().endsWith('.avi') ||
              file.name.toLowerCase().endsWith('.webm')
            );
            if (videoFiles.length > 0) {
              filteredData[category] = videoFiles;
            }
          }
        });

        setVideos(filteredData);

        // Actualizar totales de videos por categoría en la base de datos
        const updateVideoTotals = async () => {
          try {
            const session = await getSession();
            if (session?.user?.email) {
              const categoryTotals = {
                basico: { total: 0, completed: 0 },
                intermedio: { total: 0, completed: 0 },
                avanzado: { total: 0, completed: 0 }
              };

              Object.keys(filteredData).forEach(category => {
                const categoryKey = category.toLowerCase().includes('basico') ? 'basico' :
                                   category.toLowerCase().includes('intermedio') ? 'intermedio' :
                                   category.toLowerCase().includes('avanzado') ? 'avanzado' : 'basico';

                categoryTotals[categoryKey].total += filteredData[category].length;
              });

              await fetch('/api/progress', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  action: 'update_video_totals',
                  data: {
                    categoryTotals
                  }
                })
              });
            }
          } catch (error) {
            console.error('Error updating video totals:', error);
          }
        };

        updateVideoTotals();
      } catch (error) {
        console.error("Error fetching videos:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchVideos();
  }, []);

  // Funciones de utilidad
  const getVideoId = (video) => video.name;
  
  const markAsWatched = async (videoId, videoName, category, watchTime = 0, progressPercent = 100) => {
    setWatchedVideos(prev => new Set([...prev, videoId]));

    // Guardar en la base de datos
    try {
      const session = await getSession();
      if (session?.user?.email) {
        await fetch('/api/progress', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'mark_video_watched',
            data: {
              videoId,
              videoName,
              category,
              watchTime,
              progressPercent
            }
          })
        });
      }
    } catch (error) {
      console.error('Error saving video progress:', error);
    }
  };

  const toggleFavorite = async (videoId) => {
    setFavoriteVideos(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(videoId)) {
        newFavorites.delete(videoId);
      } else {
        newFavorites.add(videoId);
      }
      return newFavorites;
    });

    // Guardar en la base de datos
    try {
      const session = await getSession();
      if (session?.user?.email) {
        await fetch('/api/progress', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'toggle_favorite',
            data: {
              videoId
            }
          })
        });
      }
    } catch (error) {
      console.error('Error saving favorite:', error);
    }
  };

  const saveNote = async (videoId, note) => {
    setVideoNotes(prev => ({
      ...prev,
      [videoId]: note
    }));

    // Guardar en la base de datos
    try {
      const session = await getSession();
      if (session?.user?.email) {
        await fetch('/api/progress', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'save_note',
            data: {
              videoId,
              note
            }
          })
        });
      }
    } catch (error) {
      console.error('Error saving note:', error);
    }
  };

  const updateProgress = (videoId, progressPercent) => {
    setProgress(prev => ({
      ...prev,
      [videoId]: progressPercent
    }));
  };

  const handleVideoSelect = (video) => {
    setSelectedVideo(video.url);
    setCurrentVideoName(video.name.split("/").pop().split(" (")[0].split(".")[0]);
    const videoId = getVideoId(video);
    setCurrentNote(videoNotes[videoId] || "");

    // Marcar como visto cuando se reproduce más del 80%
    setTimeout(() => {
      if (videoRef.current) {
        let startTime = Date.now();

        videoRef.current.addEventListener('timeupdate', () => {
          const videoElement = videoRef.current;
          const progressPercent = (videoElement.currentTime / videoElement.duration) * 100;
          updateProgress(videoId, progressPercent);

          if (progressPercent > 80) {
            const watchTime = Math.round((Date.now() - startTime) / 1000); // tiempo en segundos
            const category = video.name.split("/")[0]; // obtener categoría del path
            const videoName = video.name.split("/").pop().split(" (")[0].split(".")[0];

            markAsWatched(videoId, videoName, category, watchTime, progressPercent);
          }
        });

        // Registrar sesión de estudio cuando el video termine o se pause por mucho tiempo
        videoRef.current.addEventListener('ended', () => {
          const sessionDuration = Math.round((Date.now() - startTime) / 60000); // minutos
          if (sessionDuration > 0) {
            recordStudySession(sessionDuration, 'video');
          }
        });
      }
    }, 1000);
  };

  // Función para registrar sesiones de estudio
  const recordStudySession = async (duration, activity = 'video') => {
    try {
      const session = await getSession();
      if (session?.user?.email) {
        await fetch('/api/progress', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'add_study_session',
            data: {
              duration,
              activity
            }
          })
        });
      }
    } catch (error) {
      console.error('Error recording study session:', error);
    }
  };

  const handleSpeedChange = (speed) => {
    setPlaybackSpeed(speed);
    if (videoRef.current) {
      videoRef.current.playbackRate = speed;
    }
  };

  // Filtrar videos
  const filteredVideos = () => {
    let filtered = {};
    
    Object.keys(videos).forEach(category => {
      if (selectedCategory === "all" || category.toLowerCase().includes(selectedCategory.toLowerCase())) {
        const categoryVideos = videos[category].filter(video => 
          video.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
        if (categoryVideos.length > 0) {
          filtered[category] = categoryVideos;
        }
      }
    });

    return filtered;
  };

  // Calcular estadísticas
  const getTotalVideos = () => {
    return Object.values(videos).reduce((total, categoryVideos) => total + categoryVideos.length, 0);
  };

  const getWatchedCount = () => {
    return watchedVideos.size;
  };

  const getProgressPercentage = () => {
    const total = getTotalVideos();
    return total > 0 ? Math.round((getWatchedCount() / total) * 100) : 0;
  };

  if (isLoading) {
    return (
      <PrivateRoute>
        <div className="min-h-screen bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-20 w-20 border-t-2 border-b-2 border-secondary-500 mx-auto mb-4"></div>
            <p className="text-secondary-200 text-lg">Cargando videos...</p>
          </div>
        </div>
      </PrivateRoute>
    );
  }

  return (
    <PrivateRoute>
      <ProgressMigration />
      <div className="min-h-screen bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700">
        {/* Header */}
        <div className="bg-black/30 backdrop-blur-sm border-b border-white/10">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* Botón Regresar a Inicio */}
                <button
                  onClick={() => window.location.href = '/'}
                  className="flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 transition-all duration-200 text-secondary-200 hover:text-secondary-100"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  <span className="text-sm font-medium">Regresar a Inicio</span>
                </button>

                <div className="w-12 h-12 bg-gradient-to-r from-secondary-400 to-secondary-600 rounded-full flex items-center justify-center">
                  <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z" />
                  </svg>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-secondary-400">Academia RunningPips</h1>
                  <p className="text-secondary-200 text-sm">Ruta de Aprendizaje Avanzada</p>
                </div>
              </div>
              
              {/* Estadísticas de progreso */}
              <div className="text-right">
                <div className="flex items-center space-x-4">
                  <div className="text-center">
                    <p className="text-secondary-400 font-bold text-lg">{getWatchedCount()}/{getTotalVideos()}</p>
                    <p className="text-secondary-200 text-xs">Videos Vistos</p>
                  </div>
                  <div className="w-16 h-16 relative">
                    <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                      <path
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                        fill="none"
                        stroke="#334155"
                        strokeWidth="2"
                      />
                      <path
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                        fill="none"
                        stroke="#eab308"
                        strokeWidth="2"
                        strokeDasharray={`${getProgressPercentage()}, 100`}
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className="text-secondary-400 font-bold text-sm">{getProgressPercentage()}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex h-[calc(100vh-80px)]">
          {/* Sidebar de Videos */}
          <div className="w-96 bg-primary-800 border-r border-white/10 overflow-y-auto custom-scrollbar">
            <div className="p-6">
              {/* Búsqueda y Filtros */}
              <div className="space-y-4 mb-6">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Buscar videos..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 pl-10 text-secondary-100 placeholder-secondary-300 focus:outline-none focus:ring-2 focus:ring-secondary-500"
                  />
                  <svg className="w-5 h-5 text-secondary-300 absolute left-3 top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>

                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-2 text-secondary-100 focus:outline-none focus:ring-2 focus:ring-secondary-500"
                >
                  <option value="all">Todas las categorías</option>
                  <option value="basico">Trading Básico</option>
                  <option value="intermedio">Trading Intermedio</option>
                  <option value="avanzado">Trading Avanzado</option>
                </select>
              </div>

              {/* Lista de Videos */}
              <div className="space-y-4">
                {Object.keys(filteredVideos()).map((category, categoryIndex) => (
                  <div key={categoryIndex} className="bg-white/5 rounded-xl p-4 border border-white/10">
                    <h3 className="text-secondary-400 font-bold text-lg mb-3 flex items-center">
                      <span className="w-2 h-2 bg-secondary-500 rounded-full mr-2"></span>
                      {category.replace(/\//g, "")}
                    </h3>
                    <div className="space-y-2">
                      {filteredVideos()[category].map((video, videoIndex) => {
                        const videoId = getVideoId(video);
                        const isWatched = watchedVideos.has(videoId);
                        const isFavorite = favoriteVideos.has(videoId);
                        const videoProgress = progress[videoId] || 0;
                        
                        return (
                          <div
                            key={videoIndex}
                            className={`p-4 rounded-lg border transition-all duration-200 cursor-pointer ${
                              selectedVideo === video.url
                                ? "bg-secondary-600 border-secondary-500 text-primary-900"
                                : "bg-white/5 border-white/10 text-secondary-200 hover:bg-white/10"
                            }`}
                            onClick={() => handleVideoSelect(video)}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <div className="flex items-center space-x-2">
                                  {isWatched && (
                                    <svg className="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                    </svg>
                                  )}
                                  <span className="text-sm font-medium break-words">
                                    {video.name.split("/").pop().split(" (")[0].split(".")[0]}
                                  </span>
                                </div>
                                
                                {/* Barra de progreso */}
                                {videoProgress > 0 && (
                                  <div className="mt-2">
                                    <div className="w-full bg-white/20 rounded-full h-1">
                                      <div 
                                        className="bg-secondary-500 h-1 rounded-full transition-all duration-300"
                                        style={{ width: `${videoProgress}%` }}
                                      ></div>
                                    </div>
                                  </div>
                                )}
                              </div>
                              
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toggleFavorite(videoId);
                                }}
                                className="ml-2"
                              >
                                <svg 
                                  className={`w-4 h-4 ${isFavorite ? 'text-secondary-500' : 'text-gray-400'}`} 
                                  fill={isFavorite ? 'currentColor' : 'none'} 
                                  stroke="currentColor" 
                                  viewBox="0 0 24 24"
                                >
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                </svg>
                              </button>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Área Principal de Video */}
          <div className="flex-1 flex flex-col">
            {selectedVideo ? (
              <>
                {/* Video Player */}
                <div className="flex-1 bg-black flex items-center justify-center p-4">
                  <video
                    ref={videoRef}
                    key={selectedVideo}
                    controls
                    autoPlay
                    controlsList="nodownload"
                    className="w-full h-full max-w-full max-h-full object-contain rounded-lg"
                    onLoadedData={() => {
                      if (videoRef.current) {
                        videoRef.current.playbackRate = playbackSpeed;
                      }
                    }}
                  >
                    <source src={selectedVideo} type="video/mp4" />
                    Tu navegador no soporta el elemento de video.
                  </video>
                </div>

                {/* Controles y Información */}
                <div className="bg-primary-800 border-t border-white/10 p-6">
                  <div className="max-w-6xl mx-auto">
                    <div className="flex items-center justify-between mb-4">
                      <h2 className="text-2xl font-bold text-secondary-400">{currentVideoName}</h2>
                      
                      <div className="flex items-center space-x-4">
                        {/* Control de Velocidad */}
                        <div className="flex items-center space-x-2">
                          <span className="text-secondary-200 text-sm">Velocidad:</span>
                          <select
                            value={playbackSpeed}
                            onChange={(e) => handleSpeedChange(parseFloat(e.target.value))}
                            className="bg-white/10 border border-white/20 rounded px-2 py-1 text-secondary-100 text-sm"
                          >
                            <option value={0.5}>0.5x</option>
                            <option value={0.75}>0.75x</option>
                            <option value={1}>1x</option>
                            <option value={1.25}>1.25x</option>
                            <option value={1.5}>1.5x</option>
                            <option value={2}>2x</option>
                          </select>
                        </div>

                        {/* Botón de Notas */}
                        <button
                          onClick={() => setShowNotes(!showNotes)}
                          className={`px-4 py-2 rounded-lg transition-colors ${
                            showNotes 
                              ? 'bg-secondary-600 text-primary-900' 
                              : 'bg-white/10 text-secondary-200 hover:bg-white/20'
                          }`}
                        >
                          📝 Notas
                        </button>
                      </div>
                    </div>

                    {/* Área de Notas */}
                    {showNotes && (
                      <div className="bg-white/5 rounded-xl p-4 border border-white/10">
                        <h3 className="text-secondary-400 font-semibold mb-3">Mis Notas</h3>
                        <textarea
                          value={currentNote}
                          onChange={(e) => setCurrentNote(e.target.value)}
                          onBlur={() => saveNote(getVideoId({ name: currentVideoName }), currentNote)}
                          placeholder="Escribe tus notas sobre este video..."
                          className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-secondary-100 placeholder-secondary-300 focus:outline-none focus:ring-2 focus:ring-secondary-500 resize-none"
                          rows="4"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center">
                  <div className="w-24 h-24 bg-gradient-to-r from-secondary-500/20 to-primary-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                    <svg className="w-12 h-12 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold text-secondary-400 mb-2">Selecciona un Video</h3>
                  <p className="text-secondary-200">Elige un video del sidebar para comenzar tu aprendizaje</p>
                </div>
              </div>
            )}
          </div>
        </div>

        <style jsx global>{`
          .custom-scrollbar::-webkit-scrollbar {
            width: 8px;
          }
          .custom-scrollbar::-webkit-scrollbar-track {
            background: rgba(51, 65, 85, 0.3);
            border-radius: 4px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(234, 179, 8, 0.6);
            border-radius: 4px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(234, 179, 8, 0.8);
          }
        `}</style>
      </div>
    </PrivateRoute>
  );
};

export default VideosPage;
