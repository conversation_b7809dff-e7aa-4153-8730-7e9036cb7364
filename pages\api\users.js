import UsuarioAcademy from '../../models/userAcademyModel.js';
import connectDB from "../../lib/mongoose.js";


export default async function handler(req, res) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }
  const { userEmail } = req.query;

  try {
    await connectDB()
    const currentUser = await UsuarioAcademy.findOne({ email: userEmail });
    if (currentUser) {
      res.status(200).json(currentUser);
    } else {
      res.status(404).json({ error: "User not found" });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: error.message });
  }
}

