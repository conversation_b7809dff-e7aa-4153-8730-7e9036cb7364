import { useState } from "react";
import { MdOndemandVideo } from "react-icons/md";
import { VideoAccordion } from "../components/accordionCn";
import SidebarTailwindComponent from "./tailwind/sidebarTailwindComponent";

const SidebarComponent = ({
  collapsed,
  setCollapsed,
  videos,
  onVideoSelect,
  setNameVideo,
}) => {
  const [hoveredIndex, setHoveredIndex] = useState(null);

  return (
    <SidebarTailwindComponent>
      <VideoAccordion
        data={videos}
        setNameVideo={setNameVideo}
        onVideoSelect={onVideoSelect}
        collapsed={collapsed}
      />
    </SidebarTailwindComponent>
  );
};

export default SidebarComponent;
