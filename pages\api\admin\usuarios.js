import connectDB from '../../../lib/mongoose.js';
import { getServerSession } from "next-auth/next";
import { authOptions } from "../auth/[...nextauth].js";
import UsuarioAcademy from '../../../models/userAcademyModel.js';

export default async function handler(req, res) {
  const session = await getServerSession(req, res, authOptions);
  
  if (!session || session.user.email !== "<EMAIL>") {
    return res.status(403).json({ error: 'No autorizado' });
  }

  const { method } = req;

  try {
    await connectDB();

    switch (method) {
      case 'GET':
        const usuarios = await UsuarioAcademy.find().sort({ created: -1 });
        res.status(200).json({ usuarios });
        break;

      case 'PUT':
        const { userId, action, days } = req.body;
        const usuario = await UsuarioAcademy.findById(userId);
        
        if (!usuario) {
          return res.status(404).json({ error: 'Usuario no encontrado' });
        }

        switch (action) {
          case 'activate':
            usuario.canEnter = true;
            usuario.isActive = true;
            usuario.role = 'PREMIUM';
            if (!usuario.cutoffDate) {
              usuario.cutoffDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
            }
            break;

          case 'deactivate':
            usuario.canEnter = false;
            usuario.isActive = false;
            usuario.role = 'PENDIENTE';
            break;

          case 'updateDays':
            if (days && days > 0) {
              usuario.cutoffDate = new Date(Date.now() + days * 24 * 60 * 60 * 1000);
              usuario.canEnter = true;
              usuario.isActive = true;
              usuario.role = 'PREMIUM';
            }
            break;
        }

        await usuario.save();
        res.status(200).json({ success: true, usuario });
        break;

      default:
        res.setHeader('Allow', ['GET', 'PUT']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Error:', error);
    res.status(500).json({ error: 'Error interno del servidor' });
  }
}