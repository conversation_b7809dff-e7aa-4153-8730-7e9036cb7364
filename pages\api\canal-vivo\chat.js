import { getServerSession } from "next-auth/next";
import { authOptions } from "../auth/[...nextauth].js";

// Almacenamiento temporal de mensajes del chat (en producción usar base de datos)
const chatMessages = [];

export default async function handler(req, res) {
  const session = await getServerSession(req, res, authOptions);
  
  // Verificar autenticación
  if (!session) {
    return res.status(401).json({ error: 'No autenticado' });
  }

  const { method } = req;
  const AUTHORIZED_EMAIL = "<EMAIL>";

  try {
    switch (method) {
      case 'GET':
        // Obtener mensajes del chat
        res.status(200).json({ 
          messages: chatMessages.slice(-100) // Últimos 100 mensajes
        });
        break;

      case 'POST':
        // Enviar nuevo mensaje
        const { message, type = 'normal' } = req.body;

        if (!message || !message.trim()) {
          return res.status(400).json({ error: 'Mensaje requerido' });
        }

        const newMessage = {
          id: Date.now(),
          user: session.user.name || 'Usuario',
          email: session.user.email,
          message: message.trim(),
          timestamp: new Date().toISOString(),
          type, // 'normal', 'signal', 'announcement'
          isAnalyst: session.user.email === AUTHORIZED_EMAIL,
          isAuthorized: session.user.email === AUTHORIZED_EMAIL
        };

        // Agregar mensaje al chat
        chatMessages.push(newMessage);

        // Mantener solo los últimos 200 mensajes en memoria
        if (chatMessages.length > 200) {
          chatMessages.splice(0, chatMessages.length - 200);
        }

        res.status(201).json({ 
          message: 'Mensaje enviado',
          chatMessage: newMessage 
        });
        break;

      case 'DELETE':
        // Solo el analista autorizado puede eliminar mensajes
        if (session.user.email !== AUTHORIZED_EMAIL) {
          return res.status(403).json({ error: 'No autorizado' });
        }

        const { messageId } = req.body;
        const messageIndex = chatMessages.findIndex(m => m.id === messageId);
        
        if (messageIndex === -1) {
          return res.status(404).json({ error: 'Mensaje no encontrado' });
        }

        chatMessages.splice(messageIndex, 1);
        res.status(200).json({ message: 'Mensaje eliminado' });
        break;

      default:
        res.setHeader('Allow', ['GET', 'POST', 'DELETE']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Error en API de chat:', error);
    res.status(500).json({ 
      error: 'Error interno del servidor', 
      details: error.message 
    });
  }
}
