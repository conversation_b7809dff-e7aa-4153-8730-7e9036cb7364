/**
 * Utilidad universal para compresión de imágenes
 * Funciona en todos los dispositivos: PC, MacBook, móviles
 * Soluciona el problema del límite de 1MB de OpenAI
 */

/**
 * Comprime una imagen manteniendo calidad visual pero reduciendo tamaño
 * @param {File} file - Archivo de imagen original
 * @param {Object} options - Opciones de compresión
 * @param {number} options.maxSizeKB - Tamaño máximo en KB (default: 800)
 * @param {number} options.maxWidth - Ancho máximo en pixels (default: 1200)
 * @param {number} options.maxHeight - Alto máximo en pixels (default: 1200)
 * @param {number} options.quality - Calidad inicial JPEG (0.1-1.0, default: 0.8)
 * @param {string} options.outputFormat - Formato de salida (default: 'image/jpeg')
 * @returns {Promise<string>} - Imagen comprimida en formato base64
 */
export const compressImage = (file, options = {}) => {
  const {
    maxSizeKB = 800,
    maxWidth = 1200,
    maxHeight = 1200,
    quality = 0.8,
    outputFormat = 'image/jpeg'
  } = options;

  return new Promise((resolve, reject) => {
    // Validar que sea un archivo de imagen
    if (!file || !file.type.startsWith('image/')) {
      reject(new Error('El archivo debe ser una imagen'));
      return;
    }

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      try {
        // Calcular nuevas dimensiones manteniendo aspect ratio
        let { width, height } = img;
        
        // Redimensionar si excede los límites
        if (width > maxWidth || height > maxHeight) {
          const aspectRatio = width / height;
          
          if (width > height) {
            width = Math.min(width, maxWidth);
            height = width / aspectRatio;
          } else {
            height = Math.min(height, maxHeight);
            width = height * aspectRatio;
          }
        }
        
        // Configurar canvas
        canvas.width = width;
        canvas.height = height;
        
        // Limpiar canvas y dibujar imagen redimensionada
        ctx.clearRect(0, 0, width, height);
        ctx.drawImage(img, 0, 0, width, height);
        
        // Función recursiva para ajustar calidad hasta lograr tamaño deseado
        const tryCompress = (currentQuality) => {
          canvas.toBlob((blob) => {
            if (!blob) {
              reject(new Error('Error generando imagen comprimida'));
              return;
            }
            
            const sizeKB = blob.size / 1024;
            console.log(`🖼️ Compresión: ${sizeKB.toFixed(1)}KB con calidad ${currentQuality.toFixed(1)}`);
            
            // Si el tamaño es aceptable o la calidad ya es muy baja, usar este resultado
            if (sizeKB <= maxSizeKB || currentQuality <= 0.1) {
              // Convertir blob a base64
              const reader = new FileReader();
              reader.onload = () => {
                console.log(`✅ Imagen comprimida: ${sizeKB.toFixed(1)}KB (${width}x${height})`);
                resolve(reader.result);
              };
              reader.onerror = () => reject(new Error('Error leyendo imagen comprimida'));
              reader.readAsDataURL(blob);
            } else {
              // Reducir calidad y reintentar
              tryCompress(Math.max(0.1, currentQuality - 0.1));
            }
          }, outputFormat, currentQuality);
        };
        
        // Iniciar compresión
        tryCompress(quality);
        
      } catch (error) {
        reject(new Error(`Error procesando imagen: ${error.message}`));
      }
    };
    
    img.onerror = () => {
      reject(new Error('Error cargando imagen'));
    };
    
    // Cargar imagen original
    const reader = new FileReader();
    reader.onload = (e) => {
      img.src = e.target.result;
    };
    reader.onerror = () => reject(new Error('Error leyendo archivo'));
    reader.readAsDataURL(file);
  });
};

/**
 * Compresión específica para imágenes de perfil (cuadradas, más pequeñas)
 * @param {File} file - Archivo de imagen
 * @param {number} size - Tamaño cuadrado en pixels (default: 400)
 * @param {number} maxSizeKB - Tamaño máximo en KB (default: 200)
 * @returns {Promise<string>} - Imagen de perfil comprimida
 */
export const compressProfileImage = (file, size = 400, maxSizeKB = 200) => {
  return compressImage(file, {
    maxSizeKB,
    maxWidth: size,
    maxHeight: size,
    quality: 0.8,
    outputFormat: 'image/jpeg'
  });
};

/**
 * Compresión específica para gráficos de trading (mantiene más detalle)
 * @param {File} file - Archivo de imagen
 * @returns {Promise<string>} - Gráfico comprimido optimizado para análisis
 */
export const compressTradingChart = (file) => {
  return compressImage(file, {
    maxSizeKB: 800,
    maxWidth: 1400,
    maxHeight: 1000,
    quality: 0.85,
    outputFormat: 'image/jpeg'
  });
};

/**
 * Función de utilidad para obtener información de una imagen
 * @param {File} file - Archivo de imagen
 * @returns {Promise<Object>} - Información de la imagen
 */
export const getImageInfo = (file) => {
  return new Promise((resolve, reject) => {
    if (!file || !file.type.startsWith('image/')) {
      reject(new Error('El archivo debe ser una imagen'));
      return;
    }

    const img = new Image();
    img.onload = () => {
      resolve({
        width: img.width,
        height: img.height,
        size: file.size,
        sizeKB: (file.size / 1024).toFixed(1),
        type: file.type,
        name: file.name
      });
    };
    
    img.onerror = () => reject(new Error('Error cargando imagen'));
    
    const reader = new FileReader();
    reader.onload = (e) => {
      img.src = e.target.result;
    };
    reader.readAsDataURL(file);
  });
};

/**
 * Función para validar si una imagen necesita compresión
 * @param {File} file - Archivo de imagen
 * @param {number} maxSizeKB - Tamaño máximo permitido en KB
 * @returns {boolean} - true si necesita compresión
 */
export const needsCompression = (file, maxSizeKB = 800) => {
  if (!file) return false;
  return (file.size / 1024) > maxSizeKB;
};

/**
 * Función para detectar el tipo de dispositivo (útil para ajustar compresión)
 * @returns {Object} - Información del dispositivo
 */
export const getDeviceInfo = () => {
  const userAgent = navigator.userAgent;
  const isMac = /Mac|iPhone|iPad|iPod/.test(userAgent);
  const isWindows = /Win/.test(userAgent);
  const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
  
  return {
    isMac,
    isWindows,
    isMobile,
    hasRetinaDisplay: window.devicePixelRatio > 1,
    devicePixelRatio: window.devicePixelRatio || 1
  };
};

export default {
  compressImage,
  compressProfileImage,
  compressTradingChart,
  getImageInfo,
  needsCompression,
  getDeviceInfo
};
