import React, { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/router";
import Head from "next/head";

const AdminUsuarios = () => {
  const { data: session } = useSession();
  const router = useRouter();
  const [usuarios, setUsuarios] = useState([]);
  const [loading, setLoading] = useState(true);

  const ADMIN_EMAIL = "<EMAIL>";

  useEffect(() => {
    if (!session || session.user.email !== ADMIN_EMAIL) {
      router.push('/');
      return;
    }
    cargarUsuarios();
  }, [session]);

  const cargarUsuarios = async () => {
    try {
      const response = await fetch('/api/admin/usuarios');
      if (response.ok) {
        const data = await response.json();
        setUsuarios(data.usuarios);
      }
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleUsuario = async (userId, nuevoEstado) => {
    try {
      const response = await fetch('/api/admin/usuarios', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId,
          action: nuevoEstado ? 'activate' : 'deactivate'
        })
      });

      if (response.ok) {
        cargarUsuarios();
      }
    } catch (error) {
      console.error('Error:', error);
    }
  };

  const actualizarDias = async (userId, dias) => {
    try {
      const response = await fetch('/api/admin/usuarios', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId,
          action: 'updateDays',
          days: parseInt(dias)
        })
      });

      if (response.ok) {
        cargarUsuarios();
      }
    } catch (error) {
      console.error('Error:', error);
    }
  };

  const calcularDiasRestantes = (cutoffDate) => {
    if (!cutoffDate) return 0;
    const hoy = new Date();
    const vencimiento = new Date(cutoffDate);
    const diferencia = vencimiento - hoy;
    return Math.ceil(diferencia / (1000 * 60 * 60 * 24));
  };

  if (loading) return <div className="text-white">Cargando...</div>;

  return (
    <>
      <Head>
        <title>Administrar Usuarios</title>
      </Head>
      
      <div className="min-h-screen bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700 p-6">
        <div className="container mx-auto">
          <div className="mb-6">
            <button
              onClick={() => router.push('/')}
              className="bg-secondary-500 hover:bg-secondary-600 text-white px-4 py-2 rounded-lg"
            >
              ← Volver al Inicio
            </button>
          </div>

          <h1 className="text-3xl font-bold text-white mb-8">Administrar Usuarios</h1>

          <div className="bg-black/40 backdrop-blur-sm border border-secondary-500/30 rounded-2xl p-6">
            <div className="overflow-x-auto">
              <table className="w-full text-white">
                <thead>
                  <tr className="border-b border-secondary-500/30">
                    <th className="text-left p-3">Usuario</th>
                    <th className="text-left p-3">Email</th>
                    <th className="text-center p-3">Estado</th>
                    <th className="text-center p-3">Días Restantes</th>
                    <th className="text-center p-3">Vencimiento</th>
                    <th className="text-center p-3">Días Manuales</th>
                    <th className="text-center p-3">Acciones</th>
                  </tr>
                </thead>
                <tbody>
                  {usuarios.map((usuario) => (
                    <tr key={usuario._id} className="border-b border-secondary-500/20">
                      <td className="p-3">{usuario.name}</td>
                      <td className="p-3">{usuario.email}</td>
                      <td className="p-3 text-center">
                        <button
                          onClick={() => toggleUsuario(usuario._id, !usuario.canEnter)}
                          className={`px-3 py-1 rounded text-sm font-bold ${
                            usuario.canEnter 
                              ? 'bg-green-600 text-white' 
                              : 'bg-red-600 text-white'
                          }`}
                        >
                          {usuario.canEnter ? 'ACTIVO' : 'INACTIVO'}
                        </button>
                      </td>
                      <td className="p-3 text-center">
                        <span className={`font-bold ${
                          calcularDiasRestantes(usuario.cutoffDate) <= 5 
                            ? 'text-red-400' 
                            : 'text-green-400'
                        }`}>
                          {calcularDiasRestantes(usuario.cutoffDate)} días
                        </span>
                      </td>
                      <td className="p-3 text-center text-sm">
                        {usuario.cutoffDate 
                          ? new Date(usuario.cutoffDate).toLocaleDateString()
                          : 'Sin fecha'
                        }
                      </td>
                      <td className="p-3 text-center">
                        <input
                          type="number"
                          placeholder="30"
                          className="w-16 bg-primary-600/50 border border-secondary-500/50 rounded px-2 py-1 text-center text-white"
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              actualizarDias(usuario._id, e.target.value);
                              e.target.value = '';
                            }
                          }}
                        />
                      </td>
                      <td className="p-3 text-center">
                        <div className="flex justify-center space-x-2">
                          <button
                            onClick={() => toggleUsuario(usuario._id, true)}
                            className="bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded text-xs"
                            disabled={usuario.canEnter}
                          >
                            Activar
                          </button>
                          <button
                            onClick={() => toggleUsuario(usuario._id, false)}
                            className="bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-xs"
                            disabled={!usuario.canEnter}
                          >
                            Desactivar
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default AdminUsuarios;