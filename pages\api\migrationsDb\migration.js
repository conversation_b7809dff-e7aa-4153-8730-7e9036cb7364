import mongoose from "mongoose";
import User from "../../../models/userAcademyModel.js";
import connectDB from "../../../lib/mongoose.js";

const migrate_role = async (req, res) => {
  try {
    await connectDB()
    // Actualizar todos los documentos que no tienen el campo "creadoEn"
    const result = await User.updateMany(
      { cutoffDate: null }, // Filtramos documentos que no tienen el campo `creadoEn`
      { $set: { cutoffDate: new Date() } } // Seteamos el campo `creadoEn` con la fecha actual
    );

    console.log(`${result.nModified} documentos fueron actualizados.`);
  } catch (err) {
    console.error('Error durante la migración:', err);
  }
};

export default migrate_role;
