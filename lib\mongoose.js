/* import mongoose from 'mongoose';

const connectDB = async () => {
  if (mongoose.connection.readyState !== 1) {
    try {
      await mongoose.connect(process.env.MONGODB_URI, {
        useNewUrlParser: true,
        useUnifiedTopology: true
      });
      console.log("Connected to MongoDB");
    } catch (error) {
      console.error("Failed to connect to MongoDB", error);
    }
  }
};

export default connectDB; */
import mongoose from 'mongoose';

const connectDB = async () => {
  try {
    if (mongoose.connection.readyState !== 1) {
      await mongoose.connect(process.env.MONGODB_URI, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
      });
      console.log("Conexión a MongoDB establecida correctamente");
    }
  } catch (error) {
    console.error("Error al conectar con MongoDB:", error.message);
  }
};

export default connectDB;
