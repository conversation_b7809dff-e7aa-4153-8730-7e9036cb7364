import UsuarioAcademy from "../../../models/userAcademyModel.js";
import connectDB from "../../../lib/mongoose.js";
const updateRoleToBasico = async () =>{
  await connectDB();
  const nuevaFecha = new Date("2024-07-24T00:00:00Z");
  try {
    const result = await UsuarioAcademy.updateMany(
      {},
      { $set: { cutoffDate: nuevaFecha } }
    );
    console.log({ message: "Fechas actualizadas correctamente", result });
  } catch (error) {
    console.log({ message: "Error al actualizar fechas", error });
  }
}

export default updateRoleToBasico
