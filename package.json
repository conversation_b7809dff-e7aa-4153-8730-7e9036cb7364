{"name": "nextly-template", "version": "0.1.0", "description": "Nextly <PERSON> Page Template", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "repository": {"type": "git", "url": "https://gituhb.com/web3templates/nextly-template"}, "keywords": ["nextjs", "tailwindcss", "free"], "author": "Surjith S M", "license": "ISC", "dependencies": {"@auth/mongodb-adapter": "^2.0.13", "@emotion/css": "^11.10.6", "@emotion/react": "^11.10.6", "@emotion/server": "^11.10.0", "@emotion/styled": "^11.10.6", "@headlessui/react": "^1.7.14", "@heroicons/react": "^2.0.17", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-icons": "^1.3.0", "@tailwindcss/aspect-ratio": "^0.4.2", "aws-sdk": "^2.1584.0", "class-variance-authority": "^0.7.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "formidable": "^3.5.4", "lucide-react": "^0.383.0", "mongodb": "^6.3.0", "mongoose": "^8.1.0", "next": "^13.3.1", "next-auth": "^4.24.5", "next-themes": "^0.2.1", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.43.9", "react-icons": "^4.12.0", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "yappy-node-back-sdk": "file:yappy.tar"}, "devDependencies": {"autoprefixer": "^10.4.14", "eslint-config-prettier": "^8.8.0", "postcss": "^8.4.23", "tailwindcss": "^3.3.1"}}