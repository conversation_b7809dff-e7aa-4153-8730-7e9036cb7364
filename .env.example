# Variables de entorno para el proyecto front-rp
# ================================================

# Configuración de OpenAI para Germayori AI
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Configuración de autenticación NextAuth
SECRET=HolaMundo
NEXTAUTH_URL=http://localhost:3000

# Configuración de Google OAuth
GOOGLE_CLIENT_ID=953459603787-e8aporc59tu1dqenqm8sn410qk3l4bho.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Configuración de MongoDB
MONGODB_URI=mongodb+srv://omarcode226:<EMAIL>/test

# Configuración de AWS S3
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=JHjRy9GtHGrJ6MSE3SwzmD4j05BW1t3OJZcOT8b+
AWS_REGION=us-east-2

# Configuración de Yappy (Pagos)
MERCHANT_ID=eb7055c9-a8dd-45f6-9495-7e649ba079de
SECRET_KEY=WVBfNTQ5REVGQzAtQjVFRC0zQzAwLTgyRDktNEE2MTQyN0VCNDFGLmViNzA1NWM5LWE4ZGQtNDVmNi05NDk1LTdlNjQ5YmEwNzlkZQ==

# Configuración de administración
ADMIN_SECRET_KEY=tu_admin_secret_key

# Entorno de ejecución
NODE_ENV=development
