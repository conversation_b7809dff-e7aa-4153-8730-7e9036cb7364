// pages/api/progress.js
import UserProgress from '../../models/userProgressModel.js';
import UsuarioAcademy from '../../models/userAcademyModel.js';
import connectDB from '../../lib/mongoose.js';

export default async function handler(req, res) {
  await connectDB();
  
  const { method } = req;
  const { userEmail } = req.query;

  if (!userEmail) {
    return res.status(400).json({ error: 'Email de usuario requerido' });
  }

  try {
    switch (method) {
      case 'GET':
        // Obtener progreso del usuario
        let userProgress = await UserProgress.findOne({ userEmail });
        
        if (!userProgress) {
          // Crear progreso inicial si no existe
          userProgress = new UserProgress({
            userEmail,
            startDate: new Date(),
            lastActivity: new Date(),
            totalStudyTime: 0,
            videosWatched: [],
            studySessions: [],
            categoryProgress: {
              basico: { completed: 0, total: 0 },
              intermedio: { completed: 0, total: 0 },
              avanzado: { completed: 0, total: 0 }
            },
            videoNotes: [],
            favoriteVideos: [],
            certificates: [],
            stats: {
              loginStreak: 0,
              totalLogins: 0,
              averageSessionTime: 0
            },
            profile: {},
            overallProgress: 0
          });
          await userProgress.save();
        }
        
        // Obtener información del usuario de la academia
        const academyUser = await UsuarioAcademy.findOne({ email: userEmail });
        
        // Calcular días pagados
        let daysPaid = 0;
        if (academyUser && academyUser.cutoffDate && academyUser.created) {
          const startDate = new Date(academyUser.created);
          const endDate = new Date(academyUser.cutoffDate);
          daysPaid = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
        }
        
        // Calcular progreso general de forma segura
        const categoryProgress = userProgress.categoryProgress || {
          basico: { completed: 0, total: 0 },
          intermedio: { completed: 0, total: 0 },
          avanzado: { completed: 0, total: 0 }
        };
        
        const totalVideos = (categoryProgress.basico?.total || 0) + 
                           (categoryProgress.intermedio?.total || 0) + 
                           (categoryProgress.avanzado?.total || 0);
        
        const completedVideos = (categoryProgress.basico?.completed || 0) + 
                               (categoryProgress.intermedio?.completed || 0) + 
                               (categoryProgress.avanzado?.completed || 0);
        
        const overallProgress = totalVideos > 0 ? Math.round((completedVideos / totalVideos) * 100) : 0;
        
        // Preparar respuesta con datos calculados
        const responseData = {
          // Estadísticas principales
          tiempoEstudio: Math.round((userProgress.totalStudyTime || 0) / 60), // convertir a horas
          tiempoPagado: daysPaid,
          videosCompletados: (userProgress.videosWatched || []).filter(v => v.progress >= 80).length,
          totalVideos: totalVideos,
          progresoCurso: overallProgress,
          
          // Fechas
          fechaInicio: userProgress.startDate ? userProgress.startDate.toLocaleDateString('es-ES', {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
          }) : new Date().toLocaleDateString('es-ES'),
          proximoCertificado: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toLocaleDateString('es-ES', {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
          }),
          
          // Progreso por categoría
          categoryProgress: categoryProgress,
          
          // Videos vistos
          videosWatched: userProgress.videosWatched || [],
          
          // Estadísticas adicionales
          stats: userProgress.stats || {
            loginStreak: 0,
            totalLogins: 0,
            averageSessionTime: 0
          },
          
          // Certificados
          certificates: userProgress.certificates || [],
          
          // Perfil
          profile: userProgress.profile || {},
          
          // Sesiones de estudio
          recentSessions: (userProgress.studySessions || []).slice(-10), // últimas 10 sesiones
          
          // Actividad reciente
          lastActivity: userProgress.lastActivity || new Date()
        };
        
        res.status(200).json(responseData);
        break;

      case 'POST':
        // Actualizar progreso
        const { action, data } = req.body;
        
        let progress = await UserProgress.findOne({ userEmail });
        if (!progress) {
          progress = new UserProgress({ 
            userEmail,
            totalStudyTime: 0,
            videosWatched: [],
            studySessions: [],
            categoryProgress: {
              basico: { completed: 0, total: 0 },
              intermedio: { completed: 0, total: 0 },
              avanzado: { completed: 0, total: 0 }
            },
            videoNotes: [],
            favoriteVideos: [],
            certificates: [],
            stats: {
              loginStreak: 0,
              totalLogins: 0,
              averageSessionTime: 0
            },
            profile: {},
            overallProgress: 0
          });
        }
        
        switch (action) {
          case 'mark_video_watched':
            const { videoId, videoName, category, watchTime, progressPercent } = data;
            
            // Verificar si el video ya está marcado como visto
            const existingVideo = progress.videosWatched.find(v => v.videoId === videoId);
            
            if (existingVideo) {
              // Actualizar progreso si es mayor
              if (progressPercent > existingVideo.progress) {
                existingVideo.progress = progressPercent;
                existingVideo.watchTime = Math.max(existingVideo.watchTime, watchTime);
                existingVideo.completedAt = new Date();
              }
            } else {
              // Agregar nuevo video
              progress.videosWatched.push({
                videoId,
                videoName,
                category,
                watchTime,
                progress: progressPercent,
                completedAt: new Date()
              });
              
              // Actualizar progreso por categoría
              const categoryKey = category.toLowerCase().includes('basico') ? 'basico' :
                                 category.toLowerCase().includes('intermedio') ? 'intermedio' :
                                 category.toLowerCase().includes('avanzado') ? 'avanzado' : 'basico';
              
              if (progressPercent >= 80) { // Consideramos completado si vio más del 80%
                if (!progress.categoryProgress[categoryKey]) {
                  progress.categoryProgress[categoryKey] = { completed: 0, total: 0 };
                }
                progress.categoryProgress[categoryKey].completed += 1;
              }
            }
            
            // Agregar sesión de estudio
            const sessionDuration = Math.round(watchTime / 60); // convertir a minutos
            if (sessionDuration > 0) {
              progress.studySessions.push({
                startTime: new Date(Date.now() - sessionDuration * 60000),
                endTime: new Date(),
                duration: sessionDuration,
                activity: 'video'
              });
              
              progress.totalStudyTime += sessionDuration;
            }
            
            progress.lastActivity = new Date();
            break;
            
          case 'save_note':
            const { videoId: noteVideoId, note } = data;
            const existingNoteIndex = progress.videoNotes.findIndex(n => n.videoId === noteVideoId);
            if (existingNoteIndex >= 0) {
              progress.videoNotes[existingNoteIndex].note = note;
            } else {
              progress.videoNotes.push({
                videoId: noteVideoId,
                note,
                createdAt: new Date()
              });
            }
            break;
            
          case 'toggle_favorite':
            const { videoId: favVideoId } = data;
            const favIndex = progress.favoriteVideos.indexOf(favVideoId);
            if (favIndex >= 0) {
              progress.favoriteVideos.splice(favIndex, 1);
            } else {
              progress.favoriteVideos.push(favVideoId);
            }
            break;
            
          case 'update_profile':
            const { profileImage, bio, goals, preferences } = data;
            progress.profile = {
              ...progress.profile,
              ...(profileImage && { profileImage }),
              ...(bio && { bio }),
              ...(goals && { goals }),
              ...(preferences && { preferences })
            };
            break;
            
          case 'record_login':
            if (!progress.stats) {
              progress.stats = { loginStreak: 0, totalLogins: 0, averageSessionTime: 0 };
            }
            progress.stats.totalLogins += 1;
            progress.stats.lastLoginDate = new Date();
            progress.stats.loginStreak = 1; // Simplificado por ahora
            break;
            
          default:
            return res.status(400).json({ error: 'Acción no válida' });
        }
        
        progress.lastActivity = new Date();
        await progress.save();
        
        res.status(200).json({ success: true, message: 'Progreso actualizado' });
        break;

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Error en API de progreso:', error);
    res.status(500).json({ error: 'Error interno del servidor', details: error.message });
  }
}
