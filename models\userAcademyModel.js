import mongoose from 'mongoose';

const UsuarioAcademySchema = new mongoose.Schema({
  name: { type: String, required: true },
  email: { type: String, required: true, unique: true },
  created: { type: Date, default: Date.now },
  canEnter: { type: Boolean, default: false },
  isActive: { type: Boolean, default: false },
  role: { type: String, default: 'PENDIENTE' },
  paymentId: { type: String },
  cutoffDate: { type: Date }
});

export default mongoose.models.UsuarioAcademy || mongoose.model('UsuarioAcademy', UsuarioAcademySchema);
