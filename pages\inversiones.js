import React, { useState, useEffect } from "react";
import PrivateRoute from "../components/privateRoute";
import { useSession } from "next-auth/react";
import Head from "next/head";

const InversionesPage = () => {
  const { data: session } = useSession();
  const [capitalInicial, setCapitalInicial] = useState(3000);
  const [meses, setMeses] = useState(1);
  const [gananciaMensual, setGananciaMensual] = useState(5);
  const [showCalculation, setShowCalculation] = useState(false);

  // Opciones de ganancia mensual
  const opcionesGanancia = [
    { value: 3, label: "3% (Conservador)" },
    { value: 4, label: "4% (Moderado)" },
    { value: 5, label: "5% (Óptimo)" }
  ];

  // Cálculos
  const calcularInversion = () => {
    const gananciaDecimal = gananciaMensual / 100;
    const capitalFinal = capitalInicial * Math.pow(1 + gananciaDecimal, meses);
    const gananciaTotal = capitalFinal - capitalInicial;
    const roiTotal = ((capitalFinal - capitalInicial) / capitalInicial) * 100;
    
    return {
      capitalFinal: capitalFinal.toFixed(2),
      gananciaTotal: gananciaTotal.toFixed(2),
      roiTotal: roiTotal.toFixed(1)
    };
  };

  const handleCalcular = () => {
    setShowCalculation(true);
  };

  const resultados = calcularInversion();

  return (
    <PrivateRoute>
      <Head>
        <title>Inversiones RunningPips - Invierte con Nosotros</title>
      </Head>
      <div className="min-h-screen bg-gradient-to-br from-primary-900 via-primary-800 to-primary-700">
        {/* Header */}
        <div className="bg-black/30 backdrop-blur-sm border-b border-white/10">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* Botón Regresar a Inicio */}
                <button
                  onClick={() => window.location.href = '/'}
                  className="flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg border border-white/20 transition-all duration-200 text-secondary-200 hover:text-secondary-100"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  <span className="text-sm font-medium">Regresar a Inicio</span>
                </button>
                
                <div className="w-12 h-12 bg-gradient-to-r from-secondary-400 to-secondary-600 rounded-full flex items-center justify-center">
                  <svg className="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-secondary-400">Inversiones RunningPips</h1>
                  <p className="text-secondary-200 text-sm">Invierte con nosotros por 2 años - Retiro trimestral</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-secondary-200 text-sm">Usuario: {session?.user?.name || session?.user?.email}</p>
                <div className="flex items-center space-x-2 mt-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-green-400 text-xs font-medium">En línea</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Contenido Principal */}
        <div className="max-w-6xl mx-auto px-6 py-8">
          {/* Banner de Información */}
          <div className="bg-gradient-to-r from-secondary-500/20 to-secondary-600/20 border border-secondary-500/30 rounded-xl p-6 mb-8">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-gradient-to-r from-secondary-400 to-secondary-600 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
              <div>
                <h2 className="text-2xl font-bold text-secondary-400 mb-2">¡Invierte con RunningPips!</h2>
                <p className="text-secondary-200">
                  Únete a nuestro programa de inversión por <span className="text-secondary-400 font-semibold">2 años</span> con 
                  <span className="text-secondary-400 font-semibold"> retiros trimestrales</span>. 
                  Nuestro equipo de expertos gestiona tu capital según todas nuestras estrategias de trading.
                </p>
              </div>
            </div>
          </div>

          {/* Calculadora de Inversiones */}
          <div className="bg-black/40 backdrop-blur-sm border border-secondary-500/30 rounded-xl overflow-hidden">
            {/* Header de la Calculadora */}
            <div className="bg-gradient-to-r from-secondary-500/30 to-secondary-600/30 p-6 border-b border-secondary-500/20">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-secondary-400 to-secondary-600 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-secondary-400">Calculadora de Inversiones</h3>
              </div>
            </div>

            {/* Formulario de Cálculo */}
            <div className="p-8">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                {/* Capital Inicial */}
                <div>
                  <label className="block text-secondary-400 font-semibold mb-3 flex items-center space-x-2">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                    <span>Capital Inicial (USD)</span>
                  </label>
                  <input
                    type="number"
                    value={capitalInicial}
                    onChange={(e) => setCapitalInicial(Number(e.target.value))}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-secondary-100 text-lg font-semibold focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:border-transparent"
                    min="3000"
                    step="100"
                  />
                </div>

                {/* Meses */}
                <div>
                  <label className="block text-secondary-400 font-semibold mb-3 flex items-center space-x-2">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span>Meses</span>
                  </label>
                  <input
                    type="number"
                    value={meses}
                    onChange={(e) => setMeses(Number(e.target.value))}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-secondary-100 text-lg font-semibold focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:border-transparent"
                    min="1"
                    max="24"
                  />
                </div>

                {/* Ganancia Mensual */}
                <div>
                  <label className="block text-secondary-400 font-semibold mb-3 flex items-center space-x-2">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    <span>Ganancia Mensual (%)</span>
                  </label>
                  <select
                    value={gananciaMensual}
                    onChange={(e) => setGananciaMensual(Number(e.target.value))}
                    className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-secondary-100 text-lg font-semibold focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:border-transparent"
                  >
                    {opcionesGanancia.map((opcion) => (
                      <option key={opcion.value} value={opcion.value} className="bg-primary-800 text-secondary-100">
                        {opcion.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Botón Calcular */}
              <div className="text-center mb-8">
                <button
                  onClick={handleCalcular}
                  className="px-12 py-4 bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 text-white font-bold text-lg rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
                >
                  Calcular Inversión
                </button>
              </div>

              {/* Resultados */}
              {showCalculation && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  {/* Ganancia Total */}
                  <div className="bg-blue-500/20 border border-blue-500/30 rounded-xl p-6 text-center">
                    <div className="flex items-center justify-center mb-3">
                      <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                      </svg>
                    </div>
                    <h4 className="text-blue-400 font-semibold mb-2">💰 Ganancia Total</h4>
                    <p className="text-3xl font-bold text-blue-300">${resultados.gananciaTotal}</p>
                  </div>

                  {/* Capital Final */}
                  <div className="bg-green-500/20 border border-green-500/30 rounded-xl p-6 text-center">
                    <div className="flex items-center justify-center mb-3">
                      <svg className="w-8 h-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                      </svg>
                    </div>
                    <h4 className="text-green-400 font-semibold mb-2">✅ Capital Final</h4>
                    <p className="text-3xl font-bold text-green-300">${resultados.capitalFinal}</p>
                  </div>

                  {/* ROI Total */}
                  <div className="bg-purple-500/20 border border-purple-500/30 rounded-xl p-6 text-center">
                    <div className="flex items-center justify-center mb-3">
                      <svg className="w-8 h-8 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                      </svg>
                    </div>
                    <h4 className="text-purple-400 font-semibold mb-2">📊 ROI Total</h4>
                    <p className="text-3xl font-bold text-purple-300">{resultados.roiTotal}%</p>
                  </div>
                </div>
              )}

              {/* Resumen de Inversión */}
              {showCalculation && (
                <div className="bg-secondary-500/10 border border-secondary-500/20 rounded-xl p-6">
                  <h4 className="text-secondary-400 font-bold text-lg mb-3">📋 Resumen de Inversión:</h4>
                  <p className="text-secondary-200 text-lg">
                    Con <span className="text-secondary-400 font-semibold">${capitalInicial.toLocaleString()}</span> de capital inicial, 
                    obteniendo un <span className="text-secondary-400 font-semibold">{gananciaMensual}%</span> mensual durante{' '}
                    <span className="text-secondary-400 font-semibold">{meses} {meses === 1 ? 'mes' : 'meses'}</span>, 
                    tu ganancia será de <span className="text-green-400 font-semibold">${resultados.gananciaTotal} USD</span>.
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Información Adicional */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6">
              <h3 className="text-secondary-400 font-bold text-lg mb-4 flex items-center space-x-2">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Beneficios del Programa</span>
              </h3>
              <ul className="space-y-3 text-secondary-200">
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-secondary-400 rounded-full"></div>
                  <span>Retiros trimestrales garantizados</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-secondary-400 rounded-full"></div>
                  <span>Gestión profesional de capital</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-secondary-400 rounded-full"></div>
                  <span>Estrategias diversificadas de trading</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-secondary-400 rounded-full"></div>
                  <span>Transparencia total en operaciones</span>
                </li>
              </ul>
            </div>

            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-6">
              <h3 className="text-secondary-400 font-bold text-lg mb-4 flex items-center space-x-2">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Información Importante</span>
              </h3>
              <ul className="space-y-3 text-secondary-200">
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  <span>Inversión mínima: $3,000 USD</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  <span>Plazo fijo: 2 años</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  <span>Retiros cada 3 meses</span>
                </li>
                <li className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                  <span>Rendimientos no garantizados</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </PrivateRoute>
  );
};

export default InversionesPage;
