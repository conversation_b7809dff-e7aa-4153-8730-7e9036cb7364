// API para obtener noticias financieras en tiempo real
export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Usando NewsAPI para noticias financieras
    const API_KEY = process.env.NEWS_API_KEY || 'demo'; // Necesitarás configurar tu API key
    
    // Si no hay API key, usar noticias de ejemplo que se actualizan
    if (API_KEY === 'demo') {
      const noticiasEjemplo = [
        {
          id: `${Date.now()}-1`,
          titulo: "Fed mantiene tasas de interés estables en 5.25%-5.50%",
          resumen: "La Reserva Federal de Estados Unidos decidió mantener las tasas de interés sin cambios, señalando cautela ante la inflación persistente.",
          categoria: "economia",
          fecha: new Date().toISOString(),
          fuente: "Reuters",
          impacto: "alto",
          url: "https://www.reuters.com/markets/us/",
          timestamp: Date.now()
        },
        {
          id: `${Date.now()}-2`,
          titulo: `Bitcoin ${Math.random() > 0.5 ? 'sube' : 'baja'} ${(Math.random() * 5 + 1).toFixed(1)}% en las últimas horas`,
          resumen: "El precio de Bitcoin experimenta volatilidad significativa debido a movimientos institucionales y cambios en el sentimiento del mercado.",
          categoria: "cripto",
          fecha: new Date().toISOString(),
          fuente: "CoinDesk",
          impacto: "alto",
          url: "https://www.coindesk.com/",
          timestamp: Date.now()
        },
        {
          id: `${Date.now()}-3`,
          titulo: `EUR/USD cotiza en ${(1.0800 + Math.random() * 0.0200).toFixed(4)}`,
          resumen: "El par EUR/USD muestra movimientos importantes impulsado por datos económicos recientes de ambas regiones.",
          categoria: "forex",
          fecha: new Date().toISOString(),
          fuente: "ForexLive",
          impacto: "medio",
          url: "https://www.forexlive.com/",
          timestamp: Date.now()
        },
        {
          id: `${Date.now()}-4`,
          titulo: `Oro alcanza $${(2000 + Math.random() * 100).toFixed(0)} por onza`,
          resumen: "El precio del oro continúa su tendencia debido a la incertidumbre geopolítica y la demanda de refugio seguro.",
          categoria: "materias-primas",
          fecha: new Date().toISOString(),
          fuente: "MarketWatch",
          impacto: "alto",
          url: "https://www.marketwatch.com/",
          timestamp: Date.now()
        },
        {
          id: `${Date.now()}-5`,
          titulo: `S&P 500 ${Math.random() > 0.5 ? 'gana' : 'pierde'} ${(Math.random() * 2).toFixed(1)}% en la sesión`,
          resumen: "El índice S&P 500 muestra movimientos significativos influenciado por resultados corporativos y datos macroeconómicos.",
          categoria: "analisis",
          fecha: new Date().toISOString(),
          fuente: "TradingView",
          impacto: "medio",
          url: "https://www.tradingview.com/",
          timestamp: Date.now()
        },
        {
          id: `${Date.now()}-6`,
          titulo: `Petróleo WTI en $${(70 + Math.random() * 10).toFixed(2)} por barril`,
          resumen: "Los precios del petróleo experimentan cambios debido a factores geopolíticos y variaciones en la demanda global.",
          categoria: "materias-primas",
          fecha: new Date().toISOString(),
          fuente: "Bloomberg",
          impacto: "medio",
          url: "https://www.bloomberg.com/",
          timestamp: Date.now()
        },
        {
          id: `${Date.now()}-7`,
          titulo: "Banco Central Europeo evalúa próximas decisiones monetarias",
          resumen: "El BCE analiza las condiciones económicas actuales para determinar futuras políticas monetarias en la Eurozona.",
          categoria: "economia",
          fecha: new Date().toISOString(),
          fuente: "ECB",
          impacto: "alto",
          url: "https://www.ecb.europa.eu/",
          timestamp: Date.now()
        },
        {
          id: `${Date.now()}-8`,
          titulo: `Ethereum ${Math.random() > 0.5 ? 'supera' : 'retrocede desde'} los $${(3000 + Math.random() * 500).toFixed(0)}`,
          resumen: "Ethereum muestra actividad significativa en el mercado impulsada por desarrollos en DeFi y actualizaciones de la red.",
          categoria: "cripto",
          fecha: new Date().toISOString(),
          fuente: "CoinTelegraph",
          impacto: "medio",
          url: "https://cointelegraph.com/",
          timestamp: Date.now()
        }
      ];

      return res.status(200).json({
        success: true,
        noticias: noticiasEjemplo,
        timestamp: Date.now(),
        source: 'demo'
      });
    }

    // Intentar obtener noticias reales de NewsAPI
    const response = await fetch(
      `https://newsapi.org/v2/everything?q=forex OR bitcoin OR trading OR "stock market" OR "federal reserve" OR "central bank"&language=es&sortBy=publishedAt&pageSize=20&apiKey=${API_KEY}`
    );

    if (!response.ok) {
      throw new Error('Error al obtener noticias de NewsAPI');
    }

    const data = await response.json();
    
    const noticiasFormateadas = data.articles.map((article, index) => {
      // Determinar categoría basada en el contenido
      let categoria = "economia";
      const titulo = article.title.toLowerCase();
      const descripcion = (article.description || "").toLowerCase();
      
      if (titulo.includes("bitcoin") || titulo.includes("crypto") || descripcion.includes("bitcoin")) {
        categoria = "cripto";
      } else if (titulo.includes("forex") || titulo.includes("eur") || titulo.includes("usd")) {
        categoria = "forex";
      } else if (titulo.includes("gold") || titulo.includes("oil") || titulo.includes("oro") || titulo.includes("petróleo")) {
        categoria = "materias-primas";
      } else if (titulo.includes("analysis") || titulo.includes("chart") || titulo.includes("technical")) {
        categoria = "analisis";
      }

      // Determinar impacto basado en palabras clave
      let impacto = "medio";
      if (titulo.includes("fed") || titulo.includes("federal reserve") || titulo.includes("crisis") || titulo.includes("crash")) {
        impacto = "alto";
      } else if (titulo.includes("minor") || titulo.includes("slight") || titulo.includes("pequeño")) {
        impacto = "bajo";
      }

      return {
        id: `real-${Date.now()}-${index}`,
        titulo: article.title,
        resumen: article.description || "Sin descripción disponible",
        categoria: categoria,
        fecha: article.publishedAt,
        fuente: article.source.name,
        impacto: impacto,
        url: article.url,
        timestamp: Date.now()
      };
    });

    return res.status(200).json({
      success: true,
      noticias: noticiasFormateadas,
      timestamp: Date.now(),
      source: 'newsapi'
    });

  } catch (error) {
    console.error('Error al obtener noticias:', error);
    
    // En caso de error, devolver noticias de ejemplo
    const noticiasBackup = [
      {
        id: `backup-${Date.now()}-1`,
        titulo: "Error al cargar noticias - Modo de respaldo activado",
        resumen: "Estamos experimentando dificultades para obtener noticias en tiempo real. Mostrando contenido de respaldo.",
        categoria: "economia",
        fecha: new Date().toISOString(),
        fuente: "Sistema",
        impacto: "bajo",
        url: "#",
        timestamp: Date.now()
      }
    ];

    return res.status(200).json({
      success: true,
      noticias: noticiasBackup,
      timestamp: Date.now(),
      source: 'backup',
      error: 'Error al obtener noticias externas'
    });
  }
}
