import Coupon from '../../models/couponModel.js';
import connectDB from "../../lib/mongoose.js";


export default async function handler(req, res) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }
  const { code } = req.query;

  try {
    await connectDB()
    const currentCoupon = await Coupon.findOne({ code: code });
    if (currentCoupon) {
      res.status(200).json(true);
    } else {
      res.status(404).json({ error: "Coupon not found" });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: error.message });
  }
}
