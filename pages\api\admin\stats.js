// pages/api/admin/stats.js
import UserProgress from '../../../models/userProgressModel.js';
import UsuarioAcademy from '../../../models/userAcademyModel.js';
import connectDB from '../../../lib/mongoose.js';

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    await connectDB();
    
    // Estadísticas de usuarios de la academia
    const totalUsers = await UsuarioAcademy.countDocuments();
    const activeUsers = await UsuarioAcademy.countDocuments({ canEnter: true, isActive: true });
    
    // Estadísticas de progreso
    const totalProgressRecords = await UserProgress.countDocuments();
    
    // Usuarios con progreso
    const usersWithProgress = await UserProgress.find({}).select('userEmail overallProgress totalStudyTime videosWatched lastActivity');
    
    // Calcular estadísticas de progreso
    const progressStats = {
      totalUsersWithProgress: usersWithProgress.length,
      averageProgress: 0,
      totalStudyHours: 0,
      totalVideosWatched: 0,
      usersCompleted: 0,
      activeInLast7Days: 0,
      activeInLast30Days: 0
    };
    
    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    usersWithProgress.forEach(user => {
      progressStats.averageProgress += user.overallProgress || 0;
      progressStats.totalStudyHours += Math.round((user.totalStudyTime || 0) / 60);
      progressStats.totalVideosWatched += (user.videosWatched || []).filter(v => v.progress >= 80).length;
      
      if (user.overallProgress >= 100) {
        progressStats.usersCompleted++;
      }
      
      if (user.lastActivity && user.lastActivity >= sevenDaysAgo) {
        progressStats.activeInLast7Days++;
      }
      
      if (user.lastActivity && user.lastActivity >= thirtyDaysAgo) {
        progressStats.activeInLast30Days++;
      }
    });
    
    if (usersWithProgress.length > 0) {
      progressStats.averageProgress = Math.round(progressStats.averageProgress / usersWithProgress.length);
    }
    
    // Top usuarios por progreso
    const topUsersByProgress = usersWithProgress
      .sort((a, b) => (b.overallProgress || 0) - (a.overallProgress || 0))
      .slice(0, 10)
      .map(user => ({
        email: user.userEmail,
        progress: user.overallProgress || 0,
        studyHours: Math.round((user.totalStudyTime || 0) / 60),
        videosWatched: (user.videosWatched || []).filter(v => v.progress >= 80).length,
        lastActivity: user.lastActivity
      }));
    
    // Top usuarios por tiempo de estudio
    const topUsersByStudyTime = usersWithProgress
      .sort((a, b) => (b.totalStudyTime || 0) - (a.totalStudyTime || 0))
      .slice(0, 10)
      .map(user => ({
        email: user.userEmail,
        studyHours: Math.round((user.totalStudyTime || 0) / 60),
        progress: user.overallProgress || 0,
        videosWatched: (user.videosWatched || []).filter(v => v.progress >= 80).length,
        lastActivity: user.lastActivity
      }));
    
    // Estadísticas por categoría
    const categoryStats = await UserProgress.aggregate([
      {
        $group: {
          _id: null,
          avgBasicoCompleted: { $avg: '$categoryProgress.basico.completed' },
          avgIntermedioCompleted: { $avg: '$categoryProgress.intermedio.completed' },
          avgAvanzadoCompleted: { $avg: '$categoryProgress.avanzado.completed' },
          totalBasicoCompleted: { $sum: '$categoryProgress.basico.completed' },
          totalIntermedioCompleted: { $sum: '$categoryProgress.intermedio.completed' },
          totalAvanzadoCompleted: { $sum: '$categoryProgress.avanzado.completed' }
        }
      }
    ]);
    
    // Actividad reciente (últimos 30 días)
    const recentActivity = await UserProgress.find({
      lastActivity: { $gte: thirtyDaysAgo }
    }).select('userEmail lastActivity overallProgress').sort({ lastActivity: -1 }).limit(20);
    
    const response = {
      userStats: {
        total: totalUsers,
        active: activeUsers,
        withProgress: totalProgressRecords,
        completionRate: totalUsers > 0 ? Math.round((progressStats.usersCompleted / totalUsers) * 100) : 0
      },
      progressStats,
      categoryStats: categoryStats[0] || {
        avgBasicoCompleted: 0,
        avgIntermedioCompleted: 0,
        avgAvanzadoCompleted: 0,
        totalBasicoCompleted: 0,
        totalIntermedioCompleted: 0,
        totalAvanzadoCompleted: 0
      },
      topUsers: {
        byProgress: topUsersByProgress,
        byStudyTime: topUsersByStudyTime
      },
      recentActivity: recentActivity.map(user => ({
        email: user.userEmail,
        lastActivity: user.lastActivity,
        progress: user.overallProgress || 0
      })),
      generatedAt: new Date()
    };
    
    res.status(200).json(response);
    
  } catch (error) {
    console.error('Error obteniendo estadísticas:', error);
    res.status(500).json({ 
      error: 'Error interno del servidor', 
      details: error.message 
    });
  }
}
