// pages/api/admin/check-env.js
export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Lista de variables de entorno requeridas
  const requiredEnvVars = [
    'OPENAI_API_KEY',
    'SECRET',
    'NEXTAUTH_URL',
    'GOOGLE_CLIENT_ID',
    'GOOGLE_CLIENT_SECRET',
    'MONGODB_URI',
    'AWS_ACCESS_KEY_ID',
    'AWS_SECRET_ACCESS_KEY',
    'AWS_REGION',
    'MERCHANT_ID',
    'SECRET_KEY',
    'NODE_ENV'
  ];

  const envStatus = {};
  const missingVars = [];

  // Verificar cada variable
  requiredEnvVars.forEach(varName => {
    const value = process.env[varName];
    const isPresent = !!value;
    const isValid = value && value.length > 0 && value !== 'undefined';
    
    envStatus[varName] = {
      present: isPresent,
      valid: isValid,
      preview: isValid ? `${value.substring(0, 10)}...` : 'NOT_SET'
    };

    if (!isValid) {
      missingVars.push(varName);
    }
  });

  // Verificaciones específicas
  const openaiKeyValid = process.env.OPENAI_API_KEY?.startsWith('sk-');
  const mongoUriValid = process.env.MONGODB_URI?.includes('mongodb');
  const nextauthUrlValid = process.env.NEXTAUTH_URL?.startsWith('http');

  const specificChecks = {
    openai_key_format: openaiKeyValid,
    mongodb_uri_format: mongoUriValid,
    nextauth_url_format: nextauthUrlValid
  };

  const allValid = missingVars.length === 0 && 
                   openaiKeyValid && 
                   mongoUriValid && 
                   nextauthUrlValid;

  res.status(200).json({
    success: true,
    environment: process.env.NODE_ENV || 'development',
    all_variables_valid: allValid,
    missing_variables: missingVars,
    variable_status: envStatus,
    specific_checks: specificChecks,
    timestamp: new Date().toISOString()
  });
}
