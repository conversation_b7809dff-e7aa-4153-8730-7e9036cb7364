// pages/api/admin/membership.js
import UsuarioAcademy from '../../../models/userAcademyModel.js';
import connectDB from '../../../lib/mongoose.js';

export default async function handler(req, res) {
  await connectDB();
  
  const { method } = req;
  const { userEmail, action, adminKey } = req.body;

  // Verificación básica de seguridad (en producción usar algo más robusto)
  if (adminKey !== process.env.ADMIN_SECRET_KEY) {
    return res.status(401).json({ error: 'No autorizado' });
  }

  if (!userEmail) {
    return res.status(400).json({ error: 'Email de usuario requerido' });
  }

  try {
    switch (method) {
      case 'POST':
        const user = await UsuarioAcademy.findOne({ email: userEmail });
        
        if (!user) {
          return res.status(404).json({ error: 'Usuario no encontrado' });
        }

        switch (action) {
          case 'activate':
            // Activar membresía
            user.canEnter = true;
            user.isActive = true;
            user.role = 'PREMIUM';
            user.cutoffDate = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000); // 1 año
            await user.save();
            
            res.status(200).json({ 
              success: true, 
              message: 'Membresía activada exitosamente',
              user: {
                email: user.email,
                canEnter: user.canEnter,
                isActive: user.isActive,
                role: user.role,
                cutoffDate: user.cutoffDate
              }
            });
            break;

          case 'deactivate':
            // Desactivar membresía
            user.canEnter = false;
            user.isActive = false;
            user.role = 'PENDIENTE';
            await user.save();
            
            res.status(200).json({ 
              success: true, 
              message: 'Membresía desactivada',
              user: {
                email: user.email,
                canEnter: user.canEnter,
                isActive: user.isActive,
                role: user.role
              }
            });
            break;

          case 'extend':
            // Extender membresía por 30 días
            const currentCutoff = user.cutoffDate ? new Date(user.cutoffDate) : new Date();
            user.cutoffDate = new Date(currentCutoff.getTime() + 30 * 24 * 60 * 60 * 1000);
            user.canEnter = true;
            user.isActive = true;
            await user.save();
            
            res.status(200).json({ 
              success: true, 
              message: 'Membresía extendida por 30 días',
              user: {
                email: user.email,
                cutoffDate: user.cutoffDate,
                canEnter: user.canEnter
              }
            });
            break;

          default:
            return res.status(400).json({ error: 'Acción no válida' });
        }
        break;

      case 'GET':
        // Obtener información de membresía
        const userData = await UsuarioAcademy.findOne({ email: userEmail });
        
        if (!userData) {
          return res.status(404).json({ error: 'Usuario no encontrado' });
        }

        res.status(200).json({
          user: {
            email: userData.email,
            name: userData.name,
            canEnter: userData.canEnter,
            isActive: userData.isActive,
            role: userData.role,
            cutoffDate: userData.cutoffDate,
            created: userData.created,
            paymentId: userData.paymentId
          }
        });
        break;

      default:
        res.setHeader('Allow', ['GET', 'POST']);
        res.status(405).end(`Method ${method} Not Allowed`);
    }
  } catch (error) {
    console.error('Error en API de membresía:', error);
    res.status(500).json({ error: 'Error interno del servidor', details: error.message });
  }
}
